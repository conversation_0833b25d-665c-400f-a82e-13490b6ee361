package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 新增修改班次方案dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "ClassesSchemeAddUpdateDTO", description = "新增修改班次方案dto")
@Data
public class ClassesSchemeAddUpdateDTO {

    @ApiModelProperty(value = "排班方案id", required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "班次方案id，更新传递", required = false)
    private Long id;

    @ApiModelProperty(value = "班次方案名称", required = true)
    private String name;

    @ApiModelProperty(value = "班次配置", required = true)
    private List<ClassesConfigDTO> classesConfigVOList;

}
