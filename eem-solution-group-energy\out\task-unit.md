# 单位服务变更问题分析和解决方案

## 任务执行概述

**任务**: 1.5 单位服务变更问题分析和解决方案确定  
**数据来源**: out\问题识别.md  
**问题范围**: 基于知识库第5类"单位服务变更"  
**处理策略**: 分段读取，逐个验证，实时统计  

## 搜索执行过程

### 步骤1: 问题识别文件分析
- **文件**: `out\问题识别.md`
- **总问题数**: 147个
- **涉及文件数**: 27个
- **搜索关键词**: UnitService, UserDefineUnit, getUnit, queryUnitCoef
- **搜索结果**: 发现3个单位服务变更相关问题

### 步骤2: 源代码详细分析
- **目标文件**: TeamEnergyServiceImpl.java
- **UnitService导入**: 第23行 `import com.cet.piem.service.UnitService;`
- **UnitService声明**: 第50行 `private UnitService unitService;`
- **UserDefineUnit导入**: 第4行 `import com.cet.electric.baseconfig.common.entity.UserDefineUnit;`
- **getUnit方法使用**: 第88、186、302、404行

## 单位服务变更问题详细分析

### TeamEnergyServiceImpl.java

#### 单位服务问题 1: UnitService 服务废弃 (🔴 红色标记)
- **问题位置**: 行号 23, 50
- **废弃服务**: UnitService
- **解决方案**: 使用 EnergyUnitService 替换
- **修复操作**: 
  1. 替换导入语句: `import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;`
  2. 修改服务注入: `@Resource private EnergyUnitService energyUnitService;`
  3. 添加必要的导入: `import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;`
  4. 添加DTO导入: `import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;`
- **分类依据**: 知识库第5条单位服务变更，UnitService完全废弃

#### 单位服务问题 2: UserDefineUnit 实体变更 (🟡 黄色标记)
- **问题位置**: 行号 4, 88, 186, 257, 302
- **废弃实体**: UserDefineUnit
- **解决方案**: 使用 UserDefineUnitDTO 替换
- **修复操作**: 
  1. 替换导入语句: `import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;`
  2. 修改代码中的类型引用: `UserDefineUnit` → `UserDefineUnitDTO`
  3. 更新方法参数和返回值类型
- **分类依据**: 知识库第5条实体变更，UserDefineUnitDTO是UserDefineUnit的父类

#### 单位服务问题 3: getUnit 方法签名变更 (🔴 红色标记)
- **问题位置**: 行号 88, 186, 302, 404
- **废弃方法**: `unitService.getUnit(energyValueList, ProjectUnitClassify.ENERGY, energyType)`
- **解决方案**: 使用 `energyUnitService.queryUnitCoef(UserDefineUnitSearchDTO)` 替换
- **修复操作**: 
  1. 第88行修复:
     ```java
     // 原代码
     UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());
     
     // 新代码
     UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, energyTotal));
     ```
  2. 第186行修复:
     ```java
     // 原代码
     UserDefineUnit unit = unitService.getUnit(energyValueList, ProjectUnitClassify.ENERGY, dto.getEnergyType());
     
     // 新代码
     Double maxValue = energyValueList.stream().max(Double::compareTo).orElse(null);
     UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, maxValue));
     ```
  3. 第302行修复:
     ```java
     // 原代码
     UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energySum), ProjectUnitClassify.ENERGY, dto.getEnergyType());
     
     // 新代码
     UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, energySum));
     ```
  4. 第404行修复:
     ```java
     // 原代码
     UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energySum), ProjectUnitClassify.ENERGY, dto.getEnergyType());
     
     // 新代码
     UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, energySum));
     ```
  5. 第257行方法签名修复:
     ```java
     // 原代码
     private Double unitConversion(Double value, UserDefineUnit unit)
     
     // 新代码
     private Double unitConversion(Double value, UserDefineUnitDTO unit)
     ```
- **分类依据**: 知识库第5条方法签名变更，getUnit方法完全废弃

## 处理统计

| 统计项目 | 数量 | 说明 |
|---------|------|------|
| 搜索的问题总数 | 147 | 来自问题识别文件 |
| 涉及的文件数 | 27 | 包含编译错误的文件 |
| 单位服务变更相关问题 | 3 | UnitService废弃、UserDefineUnit变更、getUnit方法变更 |
| 需要修复的文件 | 1 | TeamEnergyServiceImpl.java |
| 需要修复的代码行 | 8 | 导入、声明、方法调用等 |

## 验证完整性

### 搜索策略验证
- ✅ **关键词覆盖**: 使用了知识库中定义的所有检测模式
- ✅ **文件覆盖**: 搜索了问题识别文件中的所有问题
- ✅ **源码验证**: 直接分析了TeamEnergyServiceImpl.java源文件
- ✅ **范围完整**: 覆盖了UnitService、UserDefineUnit、getUnit相关的所有用法

### 结果可靠性
- ✅ **数据源准确**: 基于最新的问题识别文件(147个问题)
- ✅ **搜索全面**: 使用多种关键词和模式进行搜索
- ✅ **验证充分**: 通过源代码直接分析进行了二次确认
- ✅ **解决方案完整**: 基于知识库第5条提供了详细的迁移方案

## 业务规则适配说明

根据知识库第5条业务规则：
- **能耗查询**: 传值 `ProjectUnitClassify.ENERGY`
- **产量查询**: 传值 `ProjectUnitClassify.PRODUCT`
- **分开查询**: 需要结合具体业务场景，将能耗、产量分开不同的方法做查询适配

当前TeamEnergyServiceImpl.java文件中的所有调用都是能耗相关，使用 `ProjectUnitClassify.ENERGY` 是正确的。

## 任务状态

**任务状态**: ✅ 已完成  
**处理结果**: 发现3个单位服务变更问题，提供详细解决方案  
**输出文件**: out\task-unit.md (本文件)  
**后续操作**: 需要执行1.5.1验证检查和1.5.2修复任务（如需要）

## 备注

单位服务变更是一个复杂的迁移任务，涉及：
1. 服务类的完全替换（UnitService → EnergyUnitService）
2. 实体类的变更（UserDefineUnit → UserDefineUnitDTO）
3. 方法签名的重大变更（getUnit → queryUnitCoef）
4. 业务逻辑的适配（参数构造方式变更）

所有修复方案都基于知识库第5条"单位服务变更详细信息"，确保迁移的准确性和完整性。
