package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeAddUpdateDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 排班方案表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:21
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.SCHEDULING_SCHEME)
public class SchedulingScheme extends EntityWithName {
    @ApiModelProperty("班组类型")
    @JsonProperty("classteamtype")
    private Integer classTeamType;

    @ApiModelProperty("创建时间")
    @JsonProperty("createtime")
    private Long createTime;

    @ApiModelProperty("创建人")
    private Long operator;

    @ApiModelProperty("班次方案")
    private List<ClassesScheme> classesscheme_model;

    @ApiModelProperty("班组")
    private List<TeamGroupInfo> teamgroupinfo_model;

    public SchedulingScheme() {
        this.modelLabel = ModelLabelDef.SCHEDULING_SCHEME;
    }

    public SchedulingScheme(SchedulingSchemeAddUpdateDTO dto) {
        this.id = dto.getId();
        this.name = dto.getName();
        this.classTeamType = dto.getClassTeamType();
        this.createTime = System.currentTimeMillis();
        this.operator = dto.getOperator();
        this.modelLabel = ModelLabelDef.SCHEDULING_SCHEME;
    }
}
