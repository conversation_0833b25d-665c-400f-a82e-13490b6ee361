package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 班组信息表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:27
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.TEAM_GROUP_INFO)
public class TeamGroupInfo extends EntityWithName {
    @ApiModelProperty("班组人数")
    @JsonProperty("personcount")
    private Integer personCount;

    @ApiModelProperty("班组人员id清单")
    @JsonProperty("personid")
    private String personId;

    @ApiModelProperty("班组颜色标识")
    private String color;

    public TeamGroupInfo() {
        this.modelLabel = ModelLabelDef.TEAM_GROUP_INFO;
    }
}