"""
方法信息提取器

专门用于提取完整的方法签名、参数列表、返回类型和方法体源码，
确定方法的准确行号范围。
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import javalang
from javalang.tree import MethodDeclaration, CompilationUnit

try:
    from .ast_parser import JavaASTParser
    from .models import MethodAnalysisResult, MethodNode
    from .comment_extractor import CommentExtractor, CommentFormatter
except ImportError:
    from ast_parser import JavaASTParser
    from models import MethodAnalysisResult, MethodNode
    from comment_extractor import CommentExtractor, CommentFormatter


class MethodSignatureExtractor:
    """方法签名提取器"""
    
    def __init__(self):
        """初始化方法签名提取器"""
        self.logger = logging.getLogger(__name__)
        self.ast_parser = JavaASTParser()
        self.comment_extractor = CommentExtractor()
        self.comment_formatter = CommentFormatter()
    
    def extract_complete_method_signature(self, method_node: MethodDeclaration) -> Dict[str, Any]:
        """
        提取完整的方法签名信息
        
        Args:
            method_node: 方法AST节点
            
        Returns:
            包含完整签名信息的字典
        """
        try:
            signature_info = {
                'method_name': method_node.name,
                'modifiers': self._extract_detailed_modifiers(method_node),
                'return_type': self._extract_detailed_return_type(method_node),
                'parameters': self._extract_detailed_parameters(method_node),
                'annotations': self._extract_detailed_annotations(method_node),
                'throws_clause': self._extract_throws_clause(method_node),
                'generic_parameters': self._extract_generic_parameters(method_node),
                'full_signature': self._build_full_signature(method_node)
            }
            
            return signature_info
            
        except Exception as e:
            self.logger.error(f"提取方法签名时出错: {str(e)}")
            return {}
    
    def _extract_detailed_modifiers(self, method_node: MethodDeclaration) -> List[Dict[str, str]]:
        """提取详细的修饰符信息"""
        modifiers = []
        
        if hasattr(method_node, 'modifiers') and method_node.modifiers:
            # 处理modifiers可能是set或list的情况
            modifier_list = []
            if isinstance(method_node.modifiers, (list, tuple)):
                modifier_list = list(method_node.modifiers)
            elif isinstance(method_node.modifiers, set):
                modifier_list = list(method_node.modifiers)
            else:
                modifier_list = [str(method_node.modifiers)]
            
            for modifier in modifier_list:
                modifier_info = {
                    'name': modifier,
                    'type': self._classify_modifier(modifier)
                }
                modifiers.append(modifier_info)
        
        return modifiers
    
    def _classify_modifier(self, modifier: str) -> str:
        """分类修饰符类型"""
        access_modifiers = {'public', 'private', 'protected'}
        non_access_modifiers = {'static', 'final', 'abstract', 'synchronized', 'native', 'strictfp'}
        
        if modifier in access_modifiers:
            return 'access'
        elif modifier in non_access_modifiers:
            return 'non_access'
        else:
            return 'unknown'
    
    def _extract_detailed_return_type(self, method_node: MethodDeclaration) -> Dict[str, Any]:
        """提取详细的返回类型信息"""
        if not method_node.return_type:
            return {'type': 'void', 'is_primitive': True, 'is_array': False, 'generic_args': []}
        
        return_type_info = {
            'type': self.ast_parser._get_type_string(method_node.return_type),
            'is_primitive': self._is_primitive_type(method_node.return_type),
            'is_array': self._is_array_type(method_node.return_type),
            'generic_args': self._extract_generic_arguments(method_node.return_type)
        }
        
        return return_type_info
    
    def _extract_detailed_parameters(self, method_node: MethodDeclaration) -> List[Dict[str, Any]]:
        """提取详细的参数信息"""
        parameters = []
        
        if method_node.parameters:
            for param in method_node.parameters:
                param_info = {
                    'name': param.name,
                    'type': self.ast_parser._get_parameter_type_string(param),
                    'is_primitive': self._is_primitive_type(param.type),
                    'is_array': self._is_array_type(param.type),
                    'is_varargs': getattr(param, 'varargs', False),
                    'annotations': self._extract_parameter_annotations(param),
                    'generic_args': self._extract_generic_arguments(param.type)
                }
                parameters.append(param_info)
        
        return parameters
    
    def _extract_detailed_annotations(self, method_node: MethodDeclaration) -> List[Dict[str, Any]]:
        """提取详细的注解信息"""
        annotations = []
        
        if hasattr(method_node, 'annotations') and method_node.annotations:
            for annotation in method_node.annotations:
                annotation_info = {
                    'name': annotation.name,
                    'full_name': self._get_annotation_full_name(annotation),
                    'arguments': self._extract_annotation_arguments(annotation)
                }
                annotations.append(annotation_info)
        
        return annotations
    
    def _extract_throws_clause(self, method_node: MethodDeclaration) -> List[str]:
        """提取throws子句"""
        throws_list = []
        
        if hasattr(method_node, 'throws') and method_node.throws:
            for exception_type in method_node.throws:
                throws_list.append(str(exception_type))
        
        return throws_list
    
    def _extract_generic_parameters(self, method_node: MethodDeclaration) -> List[Dict[str, Any]]:
        """提取泛型参数"""
        generic_params = []
        
        if hasattr(method_node, 'type_parameters') and method_node.type_parameters:
            for type_param in method_node.type_parameters:
                param_info = {
                    'name': type_param.name,
                    'bounds': [str(bound) for bound in (type_param.extends or [])]
                }
                generic_params.append(param_info)
        
        return generic_params
    
    def _build_full_signature(self, method_node: MethodDeclaration) -> str:
        """构建完整的方法签名字符串"""
        parts = []
        
        # 修饰符
        if hasattr(method_node, 'modifiers') and method_node.modifiers:
            # 处理modifiers可能是set或list的情况
            if isinstance(method_node.modifiers, (list, tuple)):
                modifier_list = list(method_node.modifiers)
            elif isinstance(method_node.modifiers, set):
                modifier_list = list(method_node.modifiers)
            else:
                modifier_list = [str(method_node.modifiers)]
            parts.append(' '.join(modifier_list))
        
        # 泛型参数
        if hasattr(method_node, 'type_parameters') and method_node.type_parameters:
            generic_params = []
            for type_param in method_node.type_parameters:
                param_str = type_param.name
                if hasattr(type_param, 'extends') and type_param.extends:
                    param_str += f" extends {' & '.join(str(bound) for bound in type_param.extends)}"
                generic_params.append(param_str)
            parts.append(f"<{', '.join(generic_params)}>")
        
        # 返回类型
        if method_node.return_type:
            parts.append(self.ast_parser._get_type_string(method_node.return_type))
        else:
            parts.append('void')
        
        # 方法名
        method_name_part = method_node.name
        
        # 参数列表
        param_strs = []
        if method_node.parameters:
            for param in method_node.parameters:
                param_str = f"{self.ast_parser._get_parameter_type_string(param)} {param.name}"
                if getattr(param, 'varargs', False):
                    param_str = param_str.replace('[]', '...')
                param_strs.append(param_str)
        
        method_name_part += f"({', '.join(param_strs)})"
        parts.append(method_name_part)
        
        # throws子句
        if hasattr(method_node, 'throws') and method_node.throws:
            throws_str = f"throws {', '.join(str(exc) for exc in method_node.throws)}"
            parts.append(throws_str)
        
        return ' '.join(parts)
    
    def _is_primitive_type(self, type_node) -> bool:
        """检查是否为基本类型"""
        if hasattr(type_node, 'name'):
            primitive_types = {'boolean', 'byte', 'char', 'short', 'int', 'long', 'float', 'double'}
            return type_node.name in primitive_types
        return False
    
    def _is_array_type(self, type_node) -> bool:
        """检查是否为数组类型"""
        return hasattr(type_node, 'dimensions') and type_node.dimensions
    
    def _extract_generic_arguments(self, type_node) -> List[str]:
        """提取泛型参数"""
        generic_args = []
        
        if hasattr(type_node, 'arguments') and type_node.arguments:
            for arg in type_node.arguments:
                if hasattr(arg, 'type'):
                    generic_args.append(self.ast_parser._get_type_string(arg.type))
                else:
                    generic_args.append(str(arg))
        
        return generic_args
    
    def _extract_parameter_annotations(self, param) -> List[str]:
        """提取参数注解"""
        annotations = []
        
        if hasattr(param, 'annotations') and param.annotations:
            for annotation in param.annotations:
                annotations.append(f"@{annotation.name}")
        
        return annotations
    
    def _get_annotation_full_name(self, annotation) -> str:
        """获取注解的完整名称"""
        # 简单实现，实际可能需要根据import语句解析
        return annotation.name
    
    def _extract_annotation_arguments(self, annotation) -> Dict[str, Any]:
        """提取注解参数"""
        arguments = {}
        
        if hasattr(annotation, 'element') and annotation.element:
            # 处理注解参数，这里简化处理
            arguments['value'] = str(annotation.element)
        
        return arguments
    
    def extract_method_with_comments(self, method_node: MethodDeclaration, file_content: str) -> Dict[str, Any]:
        """
        提取方法的完整信息，包括注释和文档
        
        Args:
            method_node: 方法AST节点
            file_content: 文件内容
            
        Returns:
            包含方法和注释信息的完整字典
        """
        try:
            # 提取基本方法签名信息
            signature_info = self.extract_complete_method_signature(method_node)
            
            # 获取方法的行号范围
            method_start_line = 0
            if hasattr(method_node, 'position') and method_node.position:
                method_start_line = method_node.position.line
            else:
                # 通过文本搜索确定方法位置
                method_start_line = self._find_method_line_in_content(file_content, method_node.name)
            
            # 估算方法结束行
            method_end_line = self._estimate_method_end_line(file_content, method_start_line, method_node.name)
            
            # 提取方法相关的所有注释
            comment_info = self.comment_extractor.extract_method_comments(
                file_content, method_start_line, method_end_line
            )
            
            # 整合所有信息
            complete_info = {
                'signature': signature_info,
                'comments': comment_info,
                'start_line': method_start_line,
                'end_line': method_end_line,
                'formatted_notes': comment_info.get('formatted_notes', ''),
                'javadoc_summary': self._extract_javadoc_summary(comment_info),
                'all_comments_text': self._extract_all_comments_text(comment_info)
            }
            
            return complete_info
            
        except Exception as e:
            self.logger.error(f"提取方法完整信息时出错: {str(e)}")
            return {}
    
    def _find_method_line_in_content(self, file_content: str, method_name: str) -> int:
        """在文件内容中查找方法的起始行号"""
        lines = file_content.split('\n')
        
        for i, line in enumerate(lines):
            if (method_name in line and 
                ('public' in line or 'private' in line or 'protected' in line or 'static' in line)):
                # 验证这是方法声明
                if '(' in line and ')' in line:
                    return i + 1  # 转换为1基索引
        
        return 0
    
    def _estimate_method_end_line(self, file_content: str, start_line: int, method_name: str) -> int:
        """估算方法结束行号"""
        if start_line <= 0:
            return 0
        
        lines = file_content.split('\n')
        start_index = start_line - 1  # 转换为0基索引
        
        brace_count = 0
        in_method = False
        
        for i in range(start_index, len(lines)):
            line = lines[i]
            
            # 简单的大括号计数
            cleaned_line = self._clean_line_for_brace_counting(line)
            
            if '{' in cleaned_line:
                brace_count += cleaned_line.count('{')
                in_method = True
            if '}' in cleaned_line:
                brace_count -= cleaned_line.count('}')
            
            if in_method and brace_count == 0:
                return i + 1  # 转换为1基索引
        
        return len(lines)
    
    def _clean_line_for_brace_counting(self, line: str) -> str:
        """清理代码行，移除字符串中的大括号"""
        result = []
        in_string = False
        i = 0
        
        while i < len(line):
            char = line[i]
            
            if in_string:
                if char == '"' and (i == 0 or line[i-1] != '\\'):
                    in_string = False
            else:
                if char == '"':
                    in_string = True
                else:
                    result.append(char)
            
            i += 1
        
        return ''.join(result)
    
    def _extract_javadoc_summary(self, comment_info: Dict[str, Any]) -> str:
        """从注释信息中提取Javadoc摘要"""
        javadoc = comment_info.get('javadoc', {})
        if not javadoc:
            return ""
        
        summary_parts = []
        
        # 描述
        if javadoc.get('description'):
            summary_parts.append(javadoc['description'])
        
        # 参数说明
        if javadoc.get('parameters'):
            param_summary = []
            for param in javadoc['parameters']:
                param_summary.append(f"{param['name']}: {param['description']}")
            if param_summary:
                summary_parts.append(f"参数: {'; '.join(param_summary)}")
        
        # 返回值说明
        if javadoc.get('return_info'):
            summary_parts.append(f"返回: {javadoc['return_info']}")
        
        return " | ".join(summary_parts)
    
    def _extract_all_comments_text(self, comment_info: Dict[str, Any]) -> str:
        """提取所有注释的文本内容"""
        all_comments = []
        
        # Javadoc注释
        javadoc = comment_info.get('javadoc', {})
        if javadoc and javadoc.get('cleaned_content'):
            all_comments.append(f"Javadoc: {javadoc['cleaned_content']}")
        
        # 前置注释
        for comment in comment_info.get('preceding_comments', []):
            if comment.get('cleaned_content'):
                all_comments.append(f"{comment['type']}: {comment['cleaned_content']}")
        
        # 内联注释
        for comment in comment_info.get('inline_comments', []):
            if comment.get('cleaned_content'):
                all_comments.append(f"内联: {comment['cleaned_content']}")
        
        return " | ".join(all_comments)


class MethodBodyExtractor:
    """方法体提取器"""
    
    def __init__(self):
        """初始化方法体提取器"""
        self.logger = logging.getLogger(__name__)
    
    def extract_complete_method_body(self, file_content: str, method_node: MethodDeclaration, 
                                   start_line: int) -> Dict[str, Any]:
        """
        提取完整的方法体源码
        
        Args:
            file_content: 文件内容
            method_node: 方法AST节点
            start_line: 方法起始行号
            
        Returns:
            包含方法体信息的字典
        """
        try:
            lines = file_content.split('\n')
            
            # 查找方法的实际起始位置（包括注释和注解）
            actual_start = self._find_method_actual_start(lines, start_line, method_node.name)
            
            # 查找方法结束位置
            method_end = self._find_method_end_position(lines, actual_start, method_node.name)
            
            # 提取完整的方法代码
            method_code = '\n'.join(lines[actual_start - 1:method_end])
            
            # 分析方法体结构
            body_analysis = self._analyze_method_body_structure(method_code)
            
            return {
                'full_code': method_code,
                'start_line': actual_start,
                'end_line': method_end,
                'line_count': method_end - actual_start + 1,
                'body_analysis': body_analysis,
                'has_javadoc': self._has_javadoc(method_code),
                'has_annotations': self._has_annotations(method_code)
            }
            
        except Exception as e:
            self.logger.error(f"提取方法体时出错: {str(e)}")
            return {}
    
    def _find_method_actual_start(self, lines: List[str], estimated_start: int, method_name: str) -> int:
        """查找方法的实际起始位置（包括注释和注解）"""
        if estimated_start <= 0:
            # 如果没有估算的起始位置，搜索方法声明
            for i, line in enumerate(lines):
                if method_name in line and ('(' in line and ')' in line):
                    estimated_start = i + 1
                    break
        
        if estimated_start <= 0:
            return 1
        
        # 转换为0基索引
        start_index = estimated_start - 1
        
        # 向前查找注释、注解和空行
        actual_start = start_index
        for i in range(start_index - 1, -1, -1):
            line = lines[i].strip()
            if (line.startswith('/**') or line.startswith('/*') or 
                line.startswith('*') or line.startswith('//') or 
                line.startswith('@') or line == ''):
                actual_start = i
            else:
                break
        
        return actual_start + 1  # 转换回1基索引
    
    def _find_method_end_position(self, lines: List[str], start_line: int, method_name: str) -> int:
        """查找方法结束位置"""
        start_index = start_line - 1  # 转换为0基索引
        brace_count = 0
        in_method = False
        
        for i in range(start_index, len(lines)):
            line = lines[i]
            
            # 跳过字符串和注释中的大括号
            cleaned_line = self._clean_line_for_brace_counting(line)
            
            if '{' in cleaned_line:
                brace_count += cleaned_line.count('{')
                in_method = True
            if '}' in cleaned_line:
                brace_count -= cleaned_line.count('}')
            
            if in_method and brace_count == 0:
                return i + 1  # 转换回1基索引
        
        return len(lines)
    
    def _clean_line_for_brace_counting(self, line: str) -> str:
        """清理代码行，移除字符串和注释中的大括号"""
        result = []
        in_string = False
        in_char = False
        in_line_comment = False
        in_block_comment = False
        i = 0
        
        while i < len(line):
            char = line[i]
            
            if in_line_comment:
                break
            
            if in_block_comment:
                if char == '*' and i + 1 < len(line) and line[i + 1] == '/':
                    in_block_comment = False
                    i += 1
                i += 1
                continue
            
            if in_string:
                if char == '"' and (i == 0 or line[i-1] != '\\'):
                    in_string = False
                i += 1
                continue
            
            if in_char:
                if char == "'" and (i == 0 or line[i-1] != '\\'):
                    in_char = False
                i += 1
                continue
            
            if char == '"':
                in_string = True
            elif char == "'":
                in_char = True
            elif char == '/' and i + 1 < len(line):
                if line[i + 1] == '/':
                    in_line_comment = True
                    break
                elif line[i + 1] == '*':
                    in_block_comment = True
                    i += 1
                else:
                    result.append(char)
            else:
                result.append(char)
            
            i += 1
        
        return ''.join(result)
    
    def _analyze_method_body_structure(self, method_code: str) -> Dict[str, Any]:
        """分析方法体结构"""
        lines = method_code.split('\n')
        
        analysis = {
            'total_lines': len(lines),
            'code_lines': 0,
            'comment_lines': 0,
            'blank_lines': 0,
            'has_nested_methods': False,
            'has_inner_classes': False,
            'complexity_indicators': {
                'if_statements': method_code.count('if '),
                'for_loops': method_code.count('for '),
                'while_loops': method_code.count('while '),
                'try_blocks': method_code.count('try '),
                'switch_statements': method_code.count('switch ')
            }
        }
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                analysis['blank_lines'] += 1
            elif stripped.startswith('//') or stripped.startswith('*') or stripped.startswith('/*'):
                analysis['comment_lines'] += 1
            else:
                analysis['code_lines'] += 1
                
                # 检查嵌套结构
                if 'class ' in stripped and 'public class' not in stripped:
                    analysis['has_inner_classes'] = True
        
        return analysis
    
    def _has_javadoc(self, method_code: str) -> bool:
        """检查是否包含Javadoc注释"""
        return '/**' in method_code and '*/' in method_code
    
    def _has_annotations(self, method_code: str) -> bool:
        """检查是否包含注解"""
        lines = method_code.split('\n')
        for line in lines:
            if line.strip().startswith('@'):
                return True
        return False


class LineRangeCalculator:
    """行号范围计算器"""
    
    def __init__(self):
        """初始化行号范围计算器"""
        self.logger = logging.getLogger(__name__)
    
    def calculate_precise_line_range(self, file_content: str, method_name: str, 
                                   class_name: Optional[str] = None) -> Tuple[int, int]:
        """
        计算方法的精确行号范围
        
        Args:
            file_content: 文件内容
            method_name: 方法名
            class_name: 类名（可选）
            
        Returns:
            (起始行号, 结束行号) 的元组
        """
        try:
            lines = file_content.split('\n')
            
            # 查找方法声明
            method_declaration_line = self._find_method_declaration_line(lines, method_name, class_name)
            if method_declaration_line == -1:
                return (0, 0)
            
            # 查找方法的实际起始位置（包括注释和注解）
            actual_start = self._find_actual_start_line(lines, method_declaration_line)
            
            # 查找方法结束位置
            method_end = self._find_method_end_line(lines, method_declaration_line)
            
            return (actual_start, method_end)
            
        except Exception as e:
            self.logger.error(f"计算行号范围时出错: {str(e)}")
            return (0, 0)
    
    def _find_method_declaration_line(self, lines: List[str], method_name: str, 
                                    class_name: Optional[str] = None) -> int:
        """查找方法声明行"""
        for i, line in enumerate(lines):
            if self._is_method_declaration_line(line, method_name):
                # 如果指定了类名，验证方法是否在正确的类中
                if class_name:
                    if self._is_method_in_class(lines, i, class_name):
                        return i
                else:
                    return i
        
        return -1
    
    def _is_method_declaration_line(self, line: str, method_name: str) -> bool:
        """检查是否为方法声明行"""
        line = line.strip()
        
        # 基本检查：包含方法名和括号
        if method_name not in line or '(' not in line or ')' not in line:
            return False
        
        # 检查是否包含访问修饰符或其他方法修饰符
        modifiers = ['public', 'private', 'protected', 'static', 'final', 'abstract', 'synchronized']
        has_modifier = any(modifier in line for modifier in modifiers)
        
        # 检查是否为方法声明而不是方法调用
        # 方法声明通常在行的开始部分有修饰符，或者紧跟在注解后面
        if has_modifier or line.startswith(method_name):
            return True
        
        return False
    
    def _is_method_in_class(self, lines: List[str], method_line: int, class_name: str) -> bool:
        """检查方法是否在指定的类中"""
        # 向前查找类声明
        for i in range(method_line - 1, -1, -1):
            line = lines[i].strip()
            if f'class {class_name}' in line or f'interface {class_name}' in line:
                return True
            # 如果遇到其他类声明，说明方法不在目标类中
            if 'class ' in line and class_name not in line:
                return False
        
        return False
    
    def _find_actual_start_line(self, lines: List[str], method_declaration_line: int) -> int:
        """查找方法的实际起始行（包括注释和注解）"""
        actual_start = method_declaration_line
        
        # 向前查找注释、注解和空行
        for i in range(method_declaration_line - 1, -1, -1):
            line = lines[i].strip()
            if (line.startswith('/**') or line.startswith('/*') or 
                line.startswith('*') or line.startswith('//') or 
                line.startswith('@') or line == ''):
                actual_start = i
            else:
                break
        
        return actual_start + 1  # 转换为1基索引
    
    def _find_method_end_line(self, lines: List[str], method_declaration_line: int) -> int:
        """查找方法结束行"""
        brace_count = 0
        in_method = False
        
        for i in range(method_declaration_line, len(lines)):
            line = lines[i]
            
            # 简单的大括号计数（忽略字符串和注释中的大括号）
            cleaned_line = self._simple_clean_line(line)
            
            if '{' in cleaned_line:
                brace_count += cleaned_line.count('{')
                in_method = True
            if '}' in cleaned_line:
                brace_count -= cleaned_line.count('}')
            
            if in_method and brace_count == 0:
                return i + 1  # 转换为1基索引
        
        return len(lines)
    
    def _simple_clean_line(self, line: str) -> str:
        """简单清理代码行（移除字符串中的大括号）"""
        # 简化版本，只处理双引号字符串
        result = []
        in_string = False
        i = 0
        
        while i < len(line):
            char = line[i]
            
            if in_string:
                if char == '"' and (i == 0 or line[i-1] != '\\'):
                    in_string = False
            else:
                if char == '"':
                    in_string = True
                else:
                    result.append(char)
            
            i += 1
        
        return ''.join(result)