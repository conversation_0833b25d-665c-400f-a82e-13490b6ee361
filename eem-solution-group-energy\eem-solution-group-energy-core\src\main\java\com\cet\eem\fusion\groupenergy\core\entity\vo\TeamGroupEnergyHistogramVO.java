package com.cet.eem.fusion.groupenergy.core.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 班组用能柱状图信息VO
 *
 * <AUTHOR>
 */
@ApiModel(value = "TeamGroupEnergyHistogramVO", description = "班组用能柱状图信息VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeamGroupEnergyHistogramVO {
    @ApiModelProperty(value = "日期")
    private Long logTime;

    @ApiModelProperty(value = "单位")
    private String energyUnit;

    @ApiModelProperty(value = "班组用能")
    private List<TeamGroupEnergy> teamGroupEnergyList;

    @Getter
    @Setter
    public static class TeamGroupEnergy{
        @ApiModelProperty(value = "班组序号")
        private Integer teamGroupNumber;

        @ApiModelProperty(value = "班组名称")
        private String teamGroupName;

        @ApiModelProperty(value = "班组用能")
        private Double energy;

        @ApiModelProperty(value = "班组颜色")
        private String color;
    }
}
