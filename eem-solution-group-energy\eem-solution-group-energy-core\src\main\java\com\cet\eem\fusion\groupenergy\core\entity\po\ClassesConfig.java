package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 班次配置表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:26
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.CLASSES_CONFIG)
public class ClassesConfig extends EntityWithName {
    @ApiModelProperty("开始时间")
    @JsonProperty("starttime")
    private Long startTime;

    @ApiModelProperty("结束时间")
    @JsonProperty("endtime")
    private Long endTime;

    @ApiModelProperty("次序")
    @JsonProperty("serialnumber")
    private Integer serialNumber;

    public ClassesConfig() {
        this.modelLabel = ModelLabelDef.CLASSES_CONFIG;
    }
}
