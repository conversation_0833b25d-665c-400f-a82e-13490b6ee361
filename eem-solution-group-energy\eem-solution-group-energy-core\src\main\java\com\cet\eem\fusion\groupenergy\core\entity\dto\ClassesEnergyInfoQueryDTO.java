package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 班次用能数据查询条件
 *
 * <AUTHOR>
 */
@ApiModel(value = "ClassesEnergyInfoQueryDTO", description = "班次用能数据查询条件")
@Data
public class ClassesEnergyInfoQueryDTO {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @ApiModelProperty(value = "节点id", required = true)
    private Long nodeId;

    @ApiModelProperty(value = "节点label", required = true)
    private String nodeLabel;

    @ApiModelProperty(value = "排班方案id", required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "能源类型", required = true)
    private Integer energyType;

    @ApiModelProperty(value = "班组id集合", required = true)
    private List<Long> teamGroupIdList;

    @ApiModelProperty(value = "班次id", required = true)
    private List<Long> classesConfigIdList;
}
