package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 班次配置dto
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ClassesConfigDTO", description = "班次配置")
public class ClassesConfigDTO {

    @ApiModelProperty(value = "班次配置次序", required = true)
    private Integer order;

    @ApiModelProperty(value = "班次配置名称", required = true)
    private String name;

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

}