"""
类文件读取器实现 - 支持读取jar包和源码文件
"""
import os
import zipfile
import subprocess
import re
from pathlib import Path
from typing import Optional, Dict, List, Tuple
from models import ClassFileInfo

try:
    from .interfaces import ClassFileReaderInterface
    from .models import ClassFileInfo
    from .logger_utils import get_logger
except ImportError:
    from interfaces import ClassFileReaderInterface
    from models import ClassFileInfo
    from logger_utils import get_logger


class ClassFileReader(ClassFileReaderInterface):
    """类文件读取器实现 - 支持jar包和源码文件"""
    
    def __init__(self, project_path: str = "../"):
        """
        初始化类文件读取器
        
        Args:
            project_path: 项目路径
        """
        self.project_path = Path(project_path).resolve()
        self.logger = get_logger(__name__)
        self._jar_cache = {}  # 缓存jar包路径
        self._classpath_cache = None  # 缓存classpath
    
    def read_class_file(self, class_name: str, project_path: str = None) -> Optional[ClassFileInfo]:
        """
        读取类文件信息
        
        Args:
            class_name: 类名（可以是简单类名或全限定名）
            project_path: 项目路径（可选）
        
        Returns:
            ClassFileInfo对象或None
        """
        if project_path:
            self.project_path = Path(project_path).resolve()
        
        self.logger.info(f"开始读取类文件: {class_name}")
        
        # 1. 首先尝试从源码中读取
        source_info = self._read_from_source(class_name)
        if source_info:
            self.logger.info(f"从源码读取成功: {class_name}")
            return source_info
        
        # 2. 然后尝试从jar包中读取
        jar_info = self._read_from_jar(class_name)
        if jar_info:
            self.logger.info(f"从jar包读取成功: {class_name}")
            return jar_info
        
        self.logger.warning(f"未找到类文件: {class_name}")
        return None
    
    def find_class_file_path(self, class_name: str, project_path: str = None) -> Optional[str]:
        """
        查找类文件路径
        
        Args:
            class_name: 类名
            project_path: 项目路径（可选）
        
        Returns:
            文件路径或None
        """
        if project_path:
            self.project_path = Path(project_path).resolve()
        
        # 1. 查找源码文件
        source_path = self._find_source_file_path(class_name)
        if source_path:
            return source_path
        
        # 2. 查找jar包路径
        jar_path = self._find_jar_file_path(class_name)
        if jar_path:
            return jar_path
        
        return None
    
    def extract_class_structure(self, file_content: str) -> Dict[str, List[str]]:
        """
        提取类结构信息
        
        Args:
            file_content: 文件内容
        
        Returns:
            类结构信息字典
        """
        structure = {
            "methods": [],
            "fields": [],
            "imports": []
        }
        
        try:
            # 提取import语句
            import_pattern = r'import\s+(?:static\s+)?([^;]+);'
            imports = re.findall(import_pattern, file_content)
            structure["imports"] = [imp.strip() for imp in imports]
            
            # 提取方法
            method_pattern = r'(?:public|private|protected|static|\s)*\s+\w+\s+(\w+)\s*\([^)]*\)\s*(?:throws\s+[^{]+)?\s*\{'
            methods = re.findall(method_pattern, file_content, re.MULTILINE)
            structure["methods"] = list(set(methods))  # 去重
            
            # 提取字段
            field_pattern = r'(?:public|private|protected|static|final|\s)*\s+\w+(?:<[^>]+>)?\s+(\w+)\s*(?:=|;)'
            fields = re.findall(field_pattern, file_content, re.MULTILINE)
            structure["fields"] = list(set(fields))  # 去重
            
            self.logger.debug(f"提取结构信息: {len(structure['methods'])} 个方法, {len(structure['fields'])} 个字段, {len(structure['imports'])} 个导入")
            
        except Exception as e:
            self.logger.error(f"提取类结构信息失败: {e}")
        
        return structure
    
    def _read_from_source(self, class_name: str) -> Optional[ClassFileInfo]:
        """从源码文件读取类信息"""
        source_path = self._find_source_file_path(class_name)
        if not source_path:
            return None
        
        try:
            with open(source_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            structure = self.extract_class_structure(content)
            
            # 提取包名
            package_match = re.search(r'package\s+([^;]+);', content)
            package_name = package_match.group(1).strip() if package_match else None
            
            # 提取简单类名
            simple_class_name = Path(source_path).stem
            
            return ClassFileInfo(
                class_name=simple_class_name,
                file_path=source_path,
                content=content,
                methods=structure["methods"],
                fields=structure["fields"],
                imports=structure["imports"],
                package_name=package_name
            )
            
        except Exception as e:
            self.logger.error(f"读取源码文件失败 {source_path}: {e}")
            return None
    
    def _read_from_jar(self, class_name: str) -> Optional[ClassFileInfo]:
        """从jar包读取类信息"""
        jar_info = self._find_class_in_jars(class_name)
        if not jar_info:
            return None
        
        jar_path = jar_info['jar_path']
        class_entry = jar_info['class_entry']
        full_qualified_name = jar_info['full_qualified_name']
        
        try:
            # 尝试读取源码（如果jar包包含源码）
            source_entry = class_entry.replace('.class', '.java')
            
            with zipfile.ZipFile(jar_path, 'r') as zipf:
                content = ""
                methods = []
                fields = []
                imports = []
                
                # 尝试读取源码
                if source_entry in zipf.namelist():
                    try:
                        with zipf.open(source_entry) as f:
                            content = f.read().decode('utf-8')
                        structure = self.extract_class_structure(content)
                        methods = structure["methods"]
                        fields = structure["fields"]
                        imports = structure["imports"]
                        self.logger.debug(f"从jar包源码读取成功: {source_entry}")
                    except Exception as e:
                        self.logger.debug(f"无法读取jar包源码 {source_entry}: {e}")
                
                # 如果没有源码，尝试从.class文件反编译获取基本信息
                if not content:
                    content = f"// 从jar包读取: {jar_path}\n// 类路径: {class_entry}\n"
                    # 这里可以扩展为使用javap或其他工具反编译
            
            # 提取包名和类名
            parts = full_qualified_name.split('.')
            simple_class_name = parts[-1] if parts else class_name
            package_name = '.'.join(parts[:-1]) if len(parts) > 1 else None
            
            return ClassFileInfo(
                class_name=simple_class_name,
                file_path=f"{jar_path}!{class_entry}",  # jar包内路径格式
                content=content,
                methods=methods,
                fields=fields,
                imports=imports,
                package_name=package_name
            )
            
        except Exception as e:
            self.logger.error(f"读取jar包文件失败 {jar_path}: {e}")
            return None
    
    def _find_source_file_path(self, class_name: str) -> Optional[str]:
        """查找源码文件路径"""
        # 常见的源码目录
        source_dirs = [
            "src/main/java",
            "src/test/java", 
            "src/java",
            "java"
        ]
        
        for source_dir in source_dirs:
            source_path = self.project_path / source_dir
            if not source_path.exists():
                continue
            
            # 如果是全限定类名，转换为路径
            if '.' in class_name:
                relative_path = class_name.replace('.', '/') + '.java'
                full_path = source_path / relative_path
                if full_path.exists():
                    return str(full_path)
            
            # 搜索简单类名
            simple_name = class_name.split('.')[-1]
            for java_file in source_path.rglob(f"{simple_name}.java"):
                return str(java_file)
        
        return None
    
    def _find_jar_file_path(self, class_name: str) -> Optional[str]:
        """查找jar包文件路径"""
        jar_info = self._find_class_in_jars(class_name)
        return jar_info['jar_path'] if jar_info else None
    
    def _find_class_in_jars(self, class_name: str) -> Optional[Dict]:
        """在jar包中查找类"""
        jar_paths = self._get_classpath_jars()
        if not jar_paths:
            return None
        
        # 准备搜索模式
        simple_name = class_name.split('.')[-1]
        suffix = f"/{simple_name}.class"
        
        for jar_path in jar_paths:
            try:
                with zipfile.ZipFile(jar_path, 'r') as zipf:
                    for entry in zipf.namelist():
                        if entry.endswith(suffix):
                            # 如果是全限定名，检查是否完全匹配
                            if '.' in class_name:
                                expected_entry = class_name.replace('.', '/') + '.class'
                                if entry != expected_entry:
                                    continue
                            
                            full_qualified_name = entry.replace('/', '.').replace('.class', '')
                            return {
                                'jar_path': jar_path,
                                'class_entry': entry,
                                'full_qualified_name': full_qualified_name
                            }
            except Exception as e:
                self.logger.debug(f"无法读取jar包 {jar_path}: {e}")
                continue
        
        return None
    
    def _get_classpath_jars(self) -> List[str]:
        """获取classpath中的jar包列表"""
        if self._classpath_cache:
            return self._classpath_cache
        
        try:
            # 使用Maven构建classpath
            classpath_file = self.project_path / "classpath.txt"
            mvn_executable = self._find_maven_executable()
            
            if not mvn_executable:
                self.logger.warning("未找到Maven可执行文件")
                return []
            
            cmd = [
                mvn_executable,
                "dependency:build-classpath",
                "-DincludeScope=compile",
                f"-Dmdep.outputFile={classpath_file}",
                "-Dmaven.main.skip=true",
                "-Dmaven.test.skip=true"
            ]
            
            self.logger.info("构建Maven classpath...")
            proc = subprocess.run(
                cmd, 
                cwd=str(self.project_path), 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, 
                text=True,
                timeout=120  # 2分钟超时
            )
            
            if proc.returncode != 0:
                self.logger.error(f"Maven执行失败: {proc.stderr}")
                return []
            
            if not classpath_file.exists():
                self.logger.error("classpath.txt文件未生成")
                return []
            
            with open(classpath_file, 'r', encoding='utf-8') as f:
                classpath_content = f.read().strip()
            
            jar_paths = [jar.strip() for jar in classpath_content.split(os.pathsep) if jar.strip()]
            self._classpath_cache = jar_paths
            
            self.logger.info(f"获取到 {len(jar_paths)} 个jar包")
            return jar_paths
            
        except Exception as e:
            self.logger.error(f"获取classpath失败: {e}")
            return []
    
    def _find_maven_executable(self) -> Optional[str]:
        """查找Maven可执行文件"""
        # 常见的Maven路径
        possible_paths = [
            "mvn.cmd",  # Windows
            "mvn",      # Linux/Mac
        ]
        
        for mvn_path in possible_paths:
            try:
                # 测试Maven是否可用
                proc = subprocess.run(
                    [mvn_path, "--version"], 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    timeout=10
                )
                if proc.returncode == 0:
                    self.logger.debug(f"找到Maven: {mvn_path}")
                    return mvn_path
            except Exception:
                continue
        
        return None
    
    def get_jar_class_list(self, jar_path: str) -> List[str]:
        """
        获取jar包中的所有类列表
        
        Args:
            jar_path: jar包路径
        
        Returns:
            类名列表
        """
        classes = []
        try:
            with zipfile.ZipFile(jar_path, 'r') as zipf:
                for entry in zipf.namelist():
                    if entry.endswith('.class') and not entry.startswith('META-INF/'):
                        class_name = entry.replace('/', '.').replace('.class', '')
                        classes.append(class_name)
        except Exception as e:
            self.logger.error(f"读取jar包类列表失败 {jar_path}: {e}")
        
        return classes
    
    def search_classes_by_pattern(self, pattern: str) -> List[Dict]:
        """
        按模式搜索类
        
        Args:
            pattern: 搜索模式（支持通配符）
        
        Returns:
            匹配的类信息列表
        """
        results = []
        jar_paths = self._get_classpath_jars()
        
        for jar_path in jar_paths:
            try:
                with zipfile.ZipFile(jar_path, 'r') as zipf:
                    for entry in zipf.namelist():
                        if entry.endswith('.class'):
                            class_name = entry.replace('/', '.').replace('.class', '')
                            if self._match_pattern(class_name, pattern):
                                results.append({
                                    'class_name': class_name.split('.')[-1],
                                    'full_qualified_name': class_name,
                                    'jar_path': jar_path,
                                    'class_entry': entry
                                })
            except Exception as e:
                self.logger.debug(f"搜索jar包失败 {jar_path}: {e}")
                continue
        
        return results
    
    def _match_pattern(self, text: str, pattern: str) -> bool:
        """简单的模式匹配"""
        import fnmatch
        return fnmatch.fnmatch(text.lower(), pattern.lower())