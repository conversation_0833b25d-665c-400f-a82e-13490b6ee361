package com.cet.eem.fusion.groupenergy.core.entity.vo;

import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班组
 *
 * <AUTHOR>
 */
@ApiModel(value = "TeamGroupInfoVO", description = "班组")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TeamGroupInfoVO {

    @ApiModelProperty(value = "班组id，更新传递")
    private Long id;

    @ApiModelProperty(value = "班组名称")
    private String name;

    @ApiModelProperty(value = "班组人数")
    private Integer personCount;

    @ApiModelProperty(value = "运维人员清单")
    private String personId;

    @ApiModelProperty(value = "班组颜色标识")
    private String personName;

    @ApiModelProperty(value = "班组颜色标识")
    private String color;

    public TeamGroupInfoVO(TeamGroupInfo teamGroupInfo) {
        this.id = teamGroupInfo.getId();
        this.name = teamGroupInfo.getName();
        this.personCount = teamGroupInfo.getPersonCount();
        this.personId = teamGroupInfo.getPersonId();
        this.color = teamGroupInfo.getColor();
    }
}
