# Java Error Analyzer PowerShell 运行脚本
# 使用方法: .\run_analyzer.ps1 [-XmlFile path] [-ProjectPath path] [-OutputDir path]

param(
    [string]$XmlFile = "..\JavaAnnotator.xml",
    [string]$ProjectPath = "..\",
    [string]$OutputDir = ".\output",
    [string]$LogLevel = "INFO"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Java Error Analyzer 启动中..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "配置参数:" -ForegroundColor Yellow
Write-Host "  XML文件: $XmlFile" -ForegroundColor White
Write-Host "  项目路径: $ProjectPath" -ForegroundColor White
Write-Host "  输出目录: $OutputDir" -ForegroundColor White
Write-Host "  日志级别: $LogLevel" -ForegroundColor White
Write-Host ""

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Python，请确保Python已安装并添加到PATH环境变量" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查XML文件是否存在
if (-not (Test-Path $XmlFile)) {
    Write-Host "错误: XML文件不存在: $XmlFile" -ForegroundColor Red
    Write-Host "请确保JavaAnnotator.xml文件存在" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "创建输出目录: $OutputDir" -ForegroundColor Green
}

Write-Host "开始分析..." -ForegroundColor Yellow

# 运行分析器
try {
    $arguments = @(
        "main.py",
        "--xml-file", $XmlFile,
        "--project-path", $ProjectPath,
        "--output-dir", $OutputDir,
        "--log-level", $LogLevel
    )
    
    $process = Start-Process -FilePath "python" -ArgumentList $arguments -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "分析完成！" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "请查看以下文件:" -ForegroundColor Yellow
        Write-Host "  - $OutputDir\问题列表.md" -ForegroundColor White
        Write-Host "  - $OutputDir\task.md" -ForegroundColor White
        Write-Host "  - java_error_analyzer.log" -ForegroundColor White
    } else {
        Write-Host ""
        Write-Host "分析过程中出现错误 (退出代码: $($process.ExitCode))" -ForegroundColor Red
        Write-Host "请查看日志文件: java_error_analyzer.log" -ForegroundColor Yellow
    }
} catch {
    Write-Host "运行分析器时发生错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "按任意键退出"