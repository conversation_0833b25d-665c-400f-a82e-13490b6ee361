"""
AST解析器实现

基于javalang库实现Java源码的AST解析功能，提供方法节点的精确定位和提取。
从error_prone_json_reporter的PreciseSourceSearcher提取核心AST解析功能。
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from pathlib import Path
import javalang
from javalang.tree import (
    MethodDeclaration, ClassDeclaration, InterfaceDeclaration, 
    CompilationUnit, EnumDeclaration, AnnotationDeclaration,
    FormalParameter, Type, ReferenceType, BasicType
)

try:
    from .interfaces import ASTParserInterface
    from .models import MethodNode, ClassNode, MethodAnalysisResult
    from .comment_extractor import CommentExtractor, CommentFormatter
except ImportError:
    # Interface removed for simplicity
    from models import MethodNode, ClassNode, MethodAnalysisResult
    from comment_extractor import CommentExtractor, CommentFormatter


class JavaASTParser:
    """Java AST解析器"""
    
    def __init__(self):
        """初始化AST解析器"""
        self.logger = logging.getLogger(__name__)
        self.ast_cache: Dict[str, CompilationUnit] = {}  # AST缓存
        self.comment_extractor = CommentExtractor()
        self.comment_formatter = CommentFormatter()
        
    def parse_java_file(self, file_content: str) -> Optional[CompilationUnit]:
        """
        解析Java文件的AST
        
        Args:
            file_content: Java文件内容
            
        Returns:
            AST对象或None（解析失败时）
        """
        try:
            ast = javalang.parse.parse(file_content)
            self.logger.debug("AST解析成功")
            return ast
        except Exception as e:
            self.logger.warning(f"AST解析失败: {str(e)}")
            return None
    
    def find_method_in_ast(self, ast: CompilationUnit, method_name: str, 
                          class_name: Optional[str] = None,
                          parameters: Optional[List[str]] = None) -> Optional[MethodDeclaration]:
        """
        在AST中查找方法
        
        Args:
            ast: AST对象
            method_name: 方法名
            class_name: 类名（可选，用于精确匹配）
            parameters: 参数类型列表（可选，用于重载方法匹配）
            
        Returns:
            方法节点或None
        """
        if not ast:
            return None
        
        matching_methods = []
        
        # 遍历AST查找方法
        for path, node in ast.filter(MethodDeclaration):
            if node.name == method_name:
                # 检查是否在正确的类中
                if class_name:
                    containing_class = self._find_containing_class(path)
                    if not containing_class or not self._is_class_match(class_name, containing_class):
                        continue
                
                # 计算匹配分数
                match_score = self._calculate_method_match_score(node, parameters)
                if match_score > 0:
                    matching_methods.append((match_score, node))
        
        # 选择最佳匹配
        if matching_methods:
            matching_methods.sort(key=lambda x: x[0], reverse=True)
            return matching_methods[0][1]
        
        return None
    
    def find_method_by_line_number(self, ast: CompilationUnit, target_line: int) -> Optional[MethodDeclaration]:
        """
        根据行号查找包含该行的方法
        
        Args:
            ast: AST对象
            target_line: 目标行号
            
        Returns:
            包含该行的方法节点或None
        """
        if not ast:
            return None
        
        # 遍历AST查找所有方法
        for path, node in ast.filter(MethodDeclaration):
            if hasattr(node, 'position') and node.position:
                method_start_line = node.position.line
                # 估算方法结束行（这里简化处理，实际可能需要更复杂的逻辑）
                # 我们可以通过查找方法体的大括号来确定结束位置
                if method_start_line <= target_line:
                    # 这个方法可能包含目标行，返回它
                    # 注意：这里简化了逻辑，实际应该检查方法的结束行
                    return node
        
        return None
    
    def extract_method_info(self, method_node: MethodDeclaration, file_content: str) -> Dict[str, Any]:
        """
        从方法节点提取信息
        
        Args:
            method_node: 方法节点
            file_content: 文件内容
            
        Returns:
            方法信息字典
        """
        try:
            # 获取方法位置信息
            start_line = 0
            if hasattr(method_node, 'position') and method_node.position:
                start_line = method_node.position.line
            else:
                # 如果AST没有位置信息，通过文本搜索确定
                start_line = self._find_method_line_in_content(file_content, method_node.name)
            
            # 提取方法体代码
            method_body = self._extract_method_body(method_node, file_content)
            
            # 计算准确的行号范围
            start_line_actual, end_line_actual = self._calculate_accurate_line_range(
                file_content, method_body, start_line, method_node.name
            )
            
            # 提取方法注释信息
            comment_info = self.comment_extractor.extract_method_comments(
                file_content, start_line_actual, end_line_actual
            )
            
            # 提取方法基本信息
            method_info = {
                'name': method_node.name,
                'modifiers': self._extract_modifiers(method_node),
                'return_type': self._extract_return_type(method_node),
                'parameters': self._extract_parameters(method_node),
                'annotations': self._extract_annotations(method_node),
                'javadoc': self._extract_javadoc_from_content(file_content, start_line_actual),
                'body': method_body,
                'start_line': start_line_actual,
                'end_line': end_line_actual,
                'signature': self.get_method_signature(method_node),
                'comments': comment_info,
                'formatted_notes': comment_info.get('formatted_notes', ''),
                'comment_summary': comment_info.get('summary', {})
            }
            
            return method_info
            
        except Exception as e:
            self.logger.error(f"提取方法信息时出错: {str(e)}")
            return {}
    
    def extract_class_info(self, ast: CompilationUnit, class_name: str) -> Optional[ClassNode]:
        """
        提取类信息
        
        Args:
            ast: AST对象
            class_name: 类名
            
        Returns:
            类节点信息或None
        """
        try:
            # 查找类声明
            class_declaration = None
            for path, node in ast.filter((ClassDeclaration, InterfaceDeclaration, EnumDeclaration)):
                if node.name == class_name:
                    class_declaration = node
                    break
            
            if not class_declaration:
                return None
            
            # 提取包名
            package_name = ast.package.name if ast.package else ""
            
            # 提取导入语句
            imports = []
            if ast.imports:
                for imp in ast.imports:
                    imports.append(f"import {imp.path};")
            
            # 提取方法信息
            methods = []
            for path, method_node in ast.filter(MethodDeclaration):
                # 检查方法是否属于当前类
                containing_class = self._find_containing_class(path)
                if containing_class == class_name:
                    method_info = self.extract_method_info(method_node, "")
                    if method_info:
                        method_node_obj = MethodNode(
                            name=method_info['name'],
                            parameters=method_info['parameters'],
                            return_type=method_info['return_type'],
                            modifiers=method_info['modifiers'],
                            annotations=method_info['annotations'],
                            body=method_info['body'],
                            javadoc=method_info['javadoc'],
                            start_line=method_info['start_line'],
                            end_line=method_info['end_line']
                        )
                        methods.append(method_node_obj)
            
            # 提取字段信息
            fields = []
            for path, field_node in ast.filter(javalang.tree.FieldDeclaration):
                containing_class = self._find_containing_class(path)
                if containing_class == class_name:
                    field_info = {
                        'name': ', '.join([declarator.name for declarator in field_node.declarators]),
                        'type': self._get_type_string(field_node.type),
                        'modifiers': field_node.modifiers or []
                    }
                    fields.append(field_info)
            
            # 提取类注解
            annotations = []
            if hasattr(class_declaration, 'annotations') and class_declaration.annotations:
                for annotation in class_declaration.annotations:
                    annotations.append(f"@{annotation.name}")
            
            return ClassNode(
                name=class_name,
                package=package_name,
                imports=imports,
                methods=methods,
                fields=fields,
                annotations=annotations
            )
            
        except Exception as e:
            self.logger.error(f"提取类信息时出错: {str(e)}")
            return None
    
    def find_methods_by_name(self, ast: CompilationUnit, method_name: str) -> List[MethodDeclaration]:
        """
        根据方法名查找所有匹配的方法（支持重载）
        
        Args:
            ast: AST对象
            method_name: 方法名
            
        Returns:
            匹配的方法节点列表
        """
        methods = []
        if not ast:
            return methods
        
        for path, node in ast.filter(MethodDeclaration):
            if node.name == method_name:
                methods.append(node)
        
        return methods
    
    def get_method_signature(self, method_node: MethodDeclaration) -> str:
        """
        获取方法签名字符串
        
        Args:
            method_node: 方法节点
            
        Returns:
            方法签名字符串
        """
        try:
            # 构建修饰符
            modifiers = self._extract_modifiers(method_node)
            modifier_str = ' '.join(modifiers) + ' ' if modifiers else ''
            
            # 构建返回类型
            return_type = self._extract_return_type(method_node)
            
            # 构建参数列表
            params = self._extract_parameters(method_node)
            param_strs = []
            for param in params:
                param_strs.append(f"{param['type']} {param['name']}")
            param_str = ', '.join(param_strs)
            
            return f"{modifier_str}{return_type} {method_node.name}({param_str})"
            
        except Exception as e:
            self.logger.error(f"获取方法签名时出错: {str(e)}")
            return f"{method_node.name}(...)"
    
    def clear_cache(self):
        """清空AST缓存"""
        self.ast_cache.clear()
        self.logger.debug("AST缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        return {
            'ast_cache_size': len(self.ast_cache)
        }
    
    # 私有辅助方法
    
    def _find_containing_class(self, path) -> Optional[str]:
        """查找包含方法的类名"""
        for node in reversed(path):
            if isinstance(node, (ClassDeclaration, InterfaceDeclaration, EnumDeclaration)):
                return node.name
        return None
    
    def _is_class_match(self, target_class: str, candidate_class: str) -> bool:
        """检查类名是否匹配"""
        if target_class == candidate_class:
            return True
        
        # 检查简单类名匹配
        target_simple = target_class.split('.')[-1]
        candidate_simple = candidate_class.split('.')[-1]
        
        return target_simple == candidate_simple
    
    def _calculate_method_match_score(self, method_node: MethodDeclaration, 
                                    parameters: Optional[List[str]] = None) -> int:
        """计算方法匹配分数"""
        if not parameters:
            return 50  # 没有参数要求，只匹配方法名
        
        # 提取方法参数类型
        method_params = self._extract_parameters(method_node)
        method_param_types = [param['type'] for param in method_params]
        
        # 检查参数匹配
        if self._is_parameter_match(method_param_types, parameters):
            return 100  # 精确参数匹配
        else:
            return 10   # 方法名匹配但参数不匹配
    
    def _is_parameter_match(self, method_params: List[str], target_params: List[str]) -> bool:
        """检查参数是否匹配"""
        if len(method_params) != len(target_params):
            return False
        
        for method_param, target_param in zip(method_params, target_params):
            if not self._is_type_compatible(method_param, target_param):
                return False
        
        return True
    
    def _is_type_compatible(self, type1: str, type2: str) -> bool:
        """检查两个类型是否兼容"""
        # 简单的类型匹配
        if type1 == type2:
            return True
        
        # 去掉包名进行简单类名匹配
        simple_type1 = type1.split('.')[-1]
        simple_type2 = type2.split('.')[-1]
        
        return simple_type1 == simple_type2
    
    def _extract_modifiers(self, method_node: MethodDeclaration) -> List[str]:
        """提取方法修饰符"""
        modifiers = []
        if hasattr(method_node, 'modifiers') and method_node.modifiers:
            # 处理modifiers可能是set或list的情况
            if isinstance(method_node.modifiers, (list, tuple)):
                modifiers = list(method_node.modifiers)
            elif isinstance(method_node.modifiers, set):
                modifiers = list(method_node.modifiers)
            else:
                modifiers = [str(method_node.modifiers)]
        return modifiers
    
    def _extract_return_type(self, method_node: MethodDeclaration) -> str:
        """提取返回类型"""
        if method_node.return_type:
            return self._get_type_string(method_node.return_type)
        return "void"
    
    def _extract_parameters(self, method_node: MethodDeclaration) -> List[Dict[str, str]]:
        """提取方法参数"""
        parameters = []
        if method_node.parameters:
            for param in method_node.parameters:
                param_info = {
                    'name': param.name,
                    'type': self._get_parameter_type_string(param)
                }
                parameters.append(param_info)
        return parameters
    
    def _extract_annotations(self, method_node: MethodDeclaration) -> List[str]:
        """提取方法注解"""
        annotations = []
        if hasattr(method_node, 'annotations') and method_node.annotations:
            for annotation in method_node.annotations:
                annotations.append(f"@{annotation.name}")
        return annotations
    
    def _extract_javadoc(self, method_node: MethodDeclaration) -> Optional[str]:
        """提取Javadoc注释"""
        # javalang库不直接提供javadoc信息，需要通过其他方式获取
        # 这里返回None，在后续的注释提取中处理
        return None
    
    def _extract_method_body(self, method_node: MethodDeclaration, file_content: str) -> str:
        """提取方法体"""
        try:
            # 获取方法的位置信息
            if hasattr(method_node, 'position') and method_node.position:
                start_line = method_node.position.line
                return self._extract_method_code_by_line(file_content, start_line, method_node.name)
            else:
                # 如果没有位置信息，通过方法名搜索
                return self._extract_method_code_by_name(file_content, method_node.name)
        except Exception as e:
            self.logger.warning(f"提取方法体失败: {str(e)}")
            return f"// Method body extraction failed for {method_node.name}"
    
    def _estimate_method_end_line(self, method_body: str, start_line: int) -> int:
        """估算方法结束行号"""
        if not method_body:
            return start_line
        
        body_lines = method_body.split('\n')
        return start_line + len(body_lines) - 1
    
    def _get_type_string(self, type_node: Type) -> str:
        """获取类型字符串表示"""
        if isinstance(type_node, BasicType):
            return type_node.name
        elif isinstance(type_node, ReferenceType):
            type_name = type_node.name
            if type_node.arguments:
                # 处理泛型类型
                args = []
                for arg in type_node.arguments:
                    if hasattr(arg, 'type'):
                        args.append(self._get_type_string(arg.type))
                    else:
                        args.append(str(arg))
                type_name += f"<{', '.join(args)}>"
            
            # 处理数组类型
            if hasattr(type_node, 'dimensions') and type_node.dimensions:
                type_name += '[]' * len(type_node.dimensions)
            
            return type_name
        else:
            return str(type_node)
    
    def _get_parameter_type_string(self, param: FormalParameter) -> str:
        """获取参数类型字符串"""
        return self._get_type_string(param.type)
    
    def _extract_method_code_by_line(self, file_content: str, start_line: int, method_name: str) -> str:
        """根据起始行号提取方法代码"""
        lines = file_content.split('\n')
        if start_line <= 0 or start_line > len(lines):
            return f"// Invalid line number {start_line} for method {method_name}"
        
        # 转换为0基索引
        start_index = start_line - 1
        
        # 向前查找方法的注释和注解
        comment_start = start_index
        for i in range(start_index - 1, -1, -1):
            line = lines[i].strip()
            if (line.startswith('/**') or line.startswith('/*') or 
                line.startswith('*') or line.startswith('//') or 
                line.startswith('@')):
                comment_start = i
            elif line == '':
                continue  # 跳过空行
            else:
                break
        
        # 查找方法结束位置
        method_end = self._find_method_end_by_braces(lines, start_index)
        
        return '\n'.join(lines[comment_start:method_end + 1])
    
    def _extract_method_code_by_name(self, file_content: str, method_name: str) -> str:
        """根据方法名搜索并提取方法代码"""
        lines = file_content.split('\n')
        
        # 查找方法声明行
        method_start = -1
        for i, line in enumerate(lines):
            if (method_name in line and 
                ('public' in line or 'private' in line or 'protected' in line or 'static' in line)):
                # 简单验证这是方法声明而不是方法调用
                if '(' in line and ('{' in line or lines[i+1:i+3]):
                    method_start = i
                    break
        
        if method_start == -1:
            return f"// Method {method_name} not found"
        
        # 向前查找注释和注解
        comment_start = method_start
        for i in range(method_start - 1, -1, -1):
            line = lines[i].strip()
            if (line.startswith('/**') or line.startswith('/*') or 
                line.startswith('*') or line.startswith('//') or 
                line.startswith('@')):
                comment_start = i
            elif line == '':
                continue
            else:
                break
        
        # 查找方法结束位置
        method_end = self._find_method_end_by_braces(lines, method_start)
        
        return '\n'.join(lines[comment_start:method_end + 1])
    
    def _find_method_end_by_braces(self, lines: List[str], start_index: int) -> int:
        """通过大括号匹配查找方法结束位置"""
        brace_count = 0
        in_method = False
        
        for i in range(start_index, len(lines)):
            line = lines[i]
            
            # 跳过字符串和注释中的大括号
            cleaned_line = self._remove_strings_and_comments(line)
            
            if '{' in cleaned_line:
                brace_count += cleaned_line.count('{')
                in_method = True
            if '}' in cleaned_line:
                brace_count -= cleaned_line.count('}')
            
            if in_method and brace_count == 0:
                return i
        
        return len(lines) - 1
    
    def _remove_strings_and_comments(self, line: str) -> str:
        """移除字符串和注释中的内容，避免误判大括号"""
        # 简单的字符串和注释处理
        result = []
        in_string = False
        in_char = False
        in_line_comment = False
        in_block_comment = False
        i = 0
        
        while i < len(line):
            char = line[i]
            
            if in_line_comment:
                break  # 行注释，跳过剩余部分
            
            if in_block_comment:
                if char == '*' and i + 1 < len(line) and line[i + 1] == '/':
                    in_block_comment = False
                    i += 1  # 跳过 '/'
                i += 1
                continue
            
            if in_string:
                if char == '"' and (i == 0 or line[i-1] != '\\'):
                    in_string = False
                i += 1
                continue
            
            if in_char:
                if char == "'" and (i == 0 or line[i-1] != '\\'):
                    in_char = False
                i += 1
                continue
            
            if char == '"':
                in_string = True
            elif char == "'":
                in_char = True
            elif char == '/' and i + 1 < len(line):
                if line[i + 1] == '/':
                    in_line_comment = True
                    break
                elif line[i + 1] == '*':
                    in_block_comment = True
                    i += 1  # 跳过 '*'
                else:
                    result.append(char)
            else:
                result.append(char)
            
            i += 1
        
        return ''.join(result)
    
    def _find_method_line_in_content(self, file_content: str, method_name: str) -> int:
        """在文件内容中查找方法的起始行号"""
        lines = file_content.split('\n')
        
        for i, line in enumerate(lines):
            if (method_name in line and 
                ('public' in line or 'private' in line or 'protected' in line or 'static' in line)):
                # 验证这是方法声明
                if '(' in line and ')' in line:
                    return i + 1  # 转换为1基索引
        
        return 0
    
    def _calculate_accurate_line_range(self, file_content: str, method_body: str, 
                                     estimated_start: int, method_name: str) -> Tuple[int, int]:
        """计算方法的准确行号范围"""
        if not method_body:
            return estimated_start, estimated_start
        
        lines = file_content.split('\n')
        method_lines = method_body.split('\n')
        
        # 查找方法体在文件中的实际位置
        start_line = estimated_start
        if estimated_start > 0 and estimated_start <= len(lines):
            # 验证估算的起始行是否正确
            estimated_line = lines[estimated_start - 1].strip()
            first_method_line = method_lines[0].strip()
            
            if estimated_line != first_method_line:
                # 重新搜索正确的起始位置
                for i, line in enumerate(lines):
                    if line.strip() == first_method_line:
                        start_line = i + 1
                        break
        
        end_line = start_line + len(method_lines) - 1
        return start_line, end_line
    
    def _extract_javadoc_from_content(self, file_content: str, method_start_line: int) -> Optional[str]:
        """从文件内容中提取Javadoc注释"""
        if method_start_line <= 0:
            return None
        
        lines = file_content.split('\n')
        javadoc_lines = []
        
        # 向前查找Javadoc注释
        for i in range(method_start_line - 2, -1, -1):
            line = lines[i].strip()
            if line.endswith('*/'):
                javadoc_lines.insert(0, line)
                # 继续向前查找直到找到开始标记
                for j in range(i - 1, -1, -1):
                    prev_line = lines[j].strip()
                    javadoc_lines.insert(0, prev_line)
                    if prev_line.startswith('/**'):
                        return '\n'.join(javadoc_lines)
                break
            elif line.startswith('*') or line.startswith('/**'):
                javadoc_lines.insert(0, line)
            elif line == '':
                continue
            else:
                break
        
        return '\n'.join(javadoc_lines) if javadoc_lines else None


class MethodExtractor:
    """方法提取器 - 结合AST解析和文本分析"""
    
    def __init__(self):
        """初始化方法提取器"""
        self.logger = logging.getLogger(__name__)
        self.ast_parser = JavaASTParser()
        self.comment_extractor = CommentExtractor()
        self.comment_formatter = CommentFormatter()
    
    def extract_method_from_file(self, file_path: str, method_name: str, 
                               class_name: Optional[str] = None,
                               parameters: Optional[List[str]] = None) -> Optional[MethodAnalysisResult]:
        """
        从文件中提取方法信息
        
        Args:
            file_path: 文件路径
            method_name: 方法名
            class_name: 类名（可选）
            parameters: 参数类型列表（可选）
            
        Returns:
            方法分析结果或None
        """
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
        except Exception as e:
            self.logger.error(f"读取文件失败 {file_path}: {str(e)}")
            return None
        
        return self.extract_method_from_content(file_content, method_name, class_name, parameters, file_path)
    
    def extract_method_from_content(self, file_content: str, method_name: str,
                                  class_name: Optional[str] = None,
                                  parameters: Optional[List[str]] = None,
                                  file_path: str = "") -> Optional[MethodAnalysisResult]:
        """
        从文件内容中提取方法信息
        
        Args:
            file_content: 文件内容
            method_name: 方法名
            class_name: 类名（可选）
            parameters: 参数类型列表（可选）
            file_path: 文件路径（用于日志）
            
        Returns:
            方法分析结果或None
        """
        try:
            # 解析AST
            ast = self.ast_parser.parse_java_file(file_content)
            if not ast:
                self.logger.warning(f"AST解析失败，使用文本解析作为备选方案")
                return self._extract_method_by_text(file_content, method_name, class_name, file_path)
            
            # 在AST中查找方法
            method_node = self.ast_parser.find_method_in_ast(ast, method_name, class_name, parameters)
            if not method_node:
                self.logger.warning(f"在AST中未找到方法 {method_name}")
                return self._extract_method_by_text(file_content, method_name, class_name, file_path)
            
            # 提取方法信息
            method_info = self.ast_parser.extract_method_info(method_node, file_content)
            if not method_info:
                return None
            
            # 提取完整的方法代码
            method_code = self._extract_complete_method_code(file_content, method_node, method_info)
            
            # 提取方法注释 - 使用增强的注释提取器
            comment_info = method_info.get('comments', {})
            method_notes = method_info.get('formatted_notes', '')
            
            # 如果没有格式化的注释，使用备用方法
            if not method_notes and comment_info:
                method_notes = self.comment_formatter.format_comments_for_markdown(comment_info)
            
            # 构建方法上下文
            context = self._build_method_context(ast, method_node, class_name)
            
            # 构建参数字典
            in_param = {}
            for param in method_info['parameters']:
                in_param[param['name']] = param['type']
            
            return MethodAnalysisResult(
                missing_method=method_name,
                in_param=in_param,
                out_return=method_info['return_type'],
                context=context,
                content=method_code,
                notes=method_notes,
                source_file=file_path,
                line_number=method_info['start_line'],
                analysis_status="success"
            )
            
        except Exception as e:
            self.logger.error(f"提取方法信息时出错: {str(e)}")
            return MethodAnalysisResult(
                missing_method=method_name,
                in_param={},
                out_return="unknown",
                context="",
                content="",
                notes="",
                source_file=file_path,
                line_number=0,
                analysis_status="failed",
                error_message=str(e)
            )
    
    def _extract_method_by_text(self, file_content: str, method_name: str, 
                              class_name: Optional[str], file_path: str) -> Optional[MethodAnalysisResult]:
        """使用文本解析作为AST解析的备选方案"""
        self.logger.info(f"使用文本解析提取方法 {method_name}")
        
        lines = file_content.split('\n')
        method_start = -1
        
        # 查找方法声明
        for i, line in enumerate(lines):
            if method_name in line and ('public' in line or 'private' in line or 'protected' in line):
                method_start = i
                break
        
        if method_start == -1:
            return None
        
        # 简单的大括号匹配找到方法结束
        brace_count = 0
        method_end = method_start
        in_method = False
        
        for i in range(method_start, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
                in_method = True
            if '}' in line:
                brace_count -= line.count('}')
            
            if in_method and brace_count == 0:
                method_end = i
                break
        
        # 提取方法代码
        method_code = '\n'.join(lines[method_start:method_end + 1])
        
        # 提取注释
        notes = self._extract_method_comments(file_content, method_start + 1)
        
        return MethodAnalysisResult(
            missing_method=method_name,
            in_param={},
            out_return="unknown",
            context=f"Text-based extraction from {class_name or 'unknown class'}",
            content=method_code,
            notes=notes,
            source_file=file_path,
            line_number=method_start + 1,
            analysis_status="partial"
        )
    
    def _extract_complete_method_code(self, file_content: str, method_node: MethodDeclaration, 
                                    method_info: Dict[str, Any]) -> str:
        """提取完整的方法代码，包括注释和注解"""
        lines = file_content.split('\n')
        method_name = method_node.name
        start_line = method_info['start_line']
        
        if start_line <= 0 or start_line > len(lines):
            return f"// 无法提取方法 {method_name} 的代码"
        
        # 向前查找方法的注释和注解
        comment_start = start_line - 1  # 转换为0基索引
        for i in range(start_line - 2, -1, -1):
            line = lines[i].strip()
            if (line.startswith('/**') or line.startswith('/*') or 
                line.startswith('*') or line.startswith('//') or 
                line.startswith('@')):
                comment_start = i
            elif line == '':
                continue  # 跳过空行
            else:
                break
        
        # 查找方法结束位置
        method_end = self._find_method_end(lines, start_line - 1, method_name)
        
        return '\n'.join(lines[comment_start:method_end + 1])
    
    def _find_method_end(self, lines: List[str], start_index: int, method_name: str) -> int:
        """查找方法结束位置"""
        brace_count = 0
        in_method = False
        
        for i in range(start_index, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
                in_method = True
            if '}' in line:
                brace_count -= line.count('}')
            
            if in_method and brace_count == 0:
                return i
        
        return len(lines) - 1
    
    def _extract_method_comments(self, file_content: str, start_line: int, end_line: int) -> str:
        """提取方法相关的注释"""
        try:
            # 导入注释提取器
            from .comment_extractor import CommentExtractor, CommentFormatter
            
            comment_extractor = CommentExtractor()
            comment_formatter = CommentFormatter()
            
            # 提取所有注释信息
            comment_info = comment_extractor.extract_method_comments(file_content, start_line, end_line)
            
            # 格式化注释用于输出
            if comment_info.get('formatted_notes'):
                return comment_info['formatted_notes']
            else:
                # 备选方案：简单提取
                return self._simple_extract_comments(file_content, start_line)
                
        except Exception as e:
            self.logger.warning(f"使用高级注释提取失败，使用简单方法: {str(e)}")
            return self._simple_extract_comments(file_content, start_line)
    
    def _simple_extract_comments(self, file_content: str, start_line: int) -> str:
        """简单的注释提取方法（备选方案）"""
        lines = file_content.split('\n')
        comments = []
        
        # 向前查找Javadoc和注释
        for i in range(start_line - 2, -1, -1):
            line = lines[i].strip()
            if line.startswith('/**') or line.startswith('/*') or line.startswith('*'):
                comments.insert(0, line)
            elif line.startswith('//'):
                comments.insert(0, line)
            elif line == '':
                continue
            else:
                break
        
        return '\n'.join(comments) if comments else ""
    
    def _build_method_context(self, ast: CompilationUnit, method_node: MethodDeclaration, 
                            class_name: Optional[str]) -> str:
        """构建方法上下文信息"""
        context_parts = []
        
        # 包信息
        if ast.package:
            context_parts.append(f"Package: {ast.package.name}")
        
        # 类信息
        if class_name:
            context_parts.append(f"Class: {class_name}")
        
        # 方法签名
        signature = self.ast_parser.get_method_signature(method_node)
        context_parts.append(f"Method: {signature}")
        
        # 导入信息
        if ast.imports:
            import_count = len(ast.imports)
            context_parts.append(f"Imports: {import_count} import statements")
        
        return '; '.join(context_parts)