package com.cet.eem.fusion.groupenergy.core.entity.vo;

import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班次配置
 *
 * <AUTHOR>
 */
@ApiModel(value = "ClassesConfigVO", description = "班次配置")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassesConfigVO {

    @ApiModelProperty(value = "班次配置id")
    private Long id;

    @ApiModelProperty(value = "班次配置次序")
    private Integer order;

    @ApiModelProperty(value = "班次配置名称")
    private String name;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    public ClassesConfigVO(ClassesConfig classesConfig) {
        this.id = classesConfig.getId();
        this.order = classesConfig.getSerialNumber();
        this.name = classesConfig.getName();
        this.startTime = classesConfig.getStartTime();
        this.endTime = classesConfig.getEndTime();
    }
}
