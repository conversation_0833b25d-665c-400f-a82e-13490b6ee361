package com.cet.eem.fusion.groupenergy.core;

import com.cet.eem.fusion.groupenergy.core.config.EemFusionGroupEnergyBeanNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：自动配置
 * <AUTHOR> (2025/8/11)
 */
@Slf4j
@Configuration
@EnableFeignClients(value = "com.cet.eem.fusion.common.feign.feign")
@ComponentScan(value = {"com.cet.eem.fusion.groupenergy"},
        nameGenerator = EemFusionGroupEnergyBeanNameGenerator.class)
public class GroupEnergyConfigAutoConfiguration {
    public GroupEnergyConfigAutoConfiguration(){
        log.info("Load eem solution 班组能耗插件.");
    }
}
