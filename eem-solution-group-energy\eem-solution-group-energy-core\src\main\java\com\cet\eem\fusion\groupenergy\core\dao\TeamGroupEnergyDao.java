package com.cet.eem.fusion.groupenergy.core.dao;

import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;
import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;

import java.util.List;

/**
 * 班组能耗dao
 *
 * <AUTHOR>
 */
public interface TeamGroupEnergyDao extends BaseModelDao<TeamGroupEnergy> {


    /**
     * 查询班组基础能耗数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param energyType   能源类型
     * @param nodeId       节点id
     * @param nodeLabel    节点类型
     * @param teamGroupIds 班组id集合
     * @return 基础能耗数据
     */
    List<TeamGroupEnergy> queryTeamGroupEnergy(Long startTime, Long endTime, Integer energyType, Long nodeId, String nodeLabel, List<Long> teamGroupIds, Integer cycle);

    /**
     * 查询班次基础能耗数据
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param energyType       能源类型
     * @param nodeId           节点id
     * @param nodeLabel        节点类型
     * @param classesConfigIds 班次id集合
     * @return 基础能耗数据
     */
    List<TeamGroupEnergy> queryClassesConfigDayEnergy(Long startTime, Long endTime, Integer energyType, Long nodeId, String nodeLabel, List<Long> classesConfigIds);

    /**
     * 查询班次基础能耗数据
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param energyType       能源类型
     * @param nodeId           节点id
     * @param nodeLabel        节点类型
     * @param classesConfigIds 班次id集合
     * @param teamGroupIdList  班组id集合
     * @return 基础能耗数据
     */
    List<TeamGroupEnergy> queryClassesConfigDayEnergy(Long startTime, Long endTime, Integer energyType, Long nodeId, String nodeLabel, List<Long> classesConfigIds, List<Long> teamGroupIdList);
}
