package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 排班配置dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "SchedulingClassesConfigDTO", description = "排班配置dto")
@Data
public class SchedulingClassesConfigDTO {

    @ApiModelProperty(value = "时间" ,required = true)
    private Long logTime;

    @ApiModelProperty(value = "班次id" ,required = true)
    private Long classesConfigId;

    @ApiModelProperty(value = "班组id" ,required = true)
    private Long teamGroupId;
}
