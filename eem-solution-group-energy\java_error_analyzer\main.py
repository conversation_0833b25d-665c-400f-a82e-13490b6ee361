#!/usr/bin/env python3
"""
Java错误分析器主程序
按照任务流程.md执行错误分析和分类
"""
import sys
import os
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from error_parser import ErrorParser
from error_grouper import ErrorGrouper
from models import ProcessingConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('error_analysis.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 检查参数
    if len(sys.argv) < 2:
        print("使用方法: python main.py <JavaAnnotator.xml路径>")
        sys.exit(1)
    
    xml_file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(xml_file_path):
        logger.error(f"文件不存在: {xml_file_path}")
        sys.exit(1)
    
    logger.info("开始Java错误分析...")
    
    try:
        # 第一步：解析XML文件
        logger.info("第一步：解析JavaAnnotator.xml文件")
        parser = ErrorParser()
        errors = parser.parse_xml(xml_file_path)
        logger.info(f"解析完成，共发现 {len(errors)} 个错误")
        
        # 第二步：错误分组和去重
        logger.info("第二步：错误分组和去重")
        grouper = ErrorGrouper()
        grouped_errors = grouper.group_and_deduplicate(errors)
        
        # 统计信息
        stats = grouper.get_error_statistics(grouped_errors)
        logger.info(f"分组完成：{stats.files_with_errors} 个文件，{stats.total_errors} 个错误")
        
        # 第三步：生成问题列表
        logger.info("第三步：生成问题列表.md")
        generate_problem_list(grouped_errors, stats)
        
        # 第四步：结合知识库进行分类
        logger.info("第四步：结合知识库进行错误分类")
        categorized_errors = categorize_errors_with_knowledge_base(grouped_errors)
        
        # 第五步：生成详细的处理任务列表
        logger.info("第五步：生成task.md处理任务列表")
        generate_task_list(categorized_errors)
        
        logger.info("错误分析完成！")
        print("\n生成的文件：")
        print("- 问题列表.md")
        print("- task.md")
        
    except Exception as e:
        logger.error(f"错误分析过程中发生异常: {e}")
        sys.exit(1)

def generate_problem_list(grouped_errors, stats):
    """生成问题列表.md文件"""
    with open("问题列表.md", "w", encoding="utf-8") as f:
        f.write("# Java代码迁移问题列表\n\n")
        f.write(f"## 统计信息\n\n")
        f.write(f"- 总文件数: {stats.files_with_errors}\n")
        f.write(f"- 总错误数: {stats.total_errors}\n")
        f.write(f"- 平均每文件错误数: {stats.average_errors_per_file}\n\n")
        
        f.write("## 错误最多的文件\n\n")
        for file_path, error_count in stats.most_problematic_files[:10]:
            f.write(f"- {file_path}: {error_count} 个错误\n")
        f.write("\n")
        
        f.write("## 按文件分组的详细错误列表\n\n")
        
        for file_path, file_errors in grouped_errors.items():
            f.write(f"### {file_path}\n\n")
            f.write(f"错误数量: {len(file_errors)}\n\n")
            
            for i, error in enumerate(file_errors, 1):
                f.write(f"#### 错误 {i}\n")
                f.write(f"- **行号**: {error.line}\n")
                f.write(f"- **描述**: {error.description}\n")
                f.write(f"- **问题元素**: `{error.highlighted_element}`\n")
                f.write(f"- **严重程度**: {error.severity}\n")
                f.write(f"- **包名**: {error.package}\n\n")

def categorize_errors_with_knowledge_base(grouped_errors):
    """结合知识库对错误进行分类"""
    categorized = {
        "import问题": [],
        "返回类型问题": [],
        "废弃服务问题": [],
        "工具类问题": [],
        "权限相关问题": [],
        "注解问题": [],
        "未识别问题": []
    }
    
    # 知识库中的关键词映射
    knowledge_patterns = {
        "import问题": [
            "Cannot resolve symbol", "cannot find symbol", "package does not exist",
            "auth", "aspect", "EnumAndOr", "OperationPermission", "OperationLog",
            "bll", "common", "log", "annotation", "constant"
        ],
        "返回类型问题": [
            "Result", "ResultWithTotal", "ResponseEntity", "Response"
        ],
        "废弃服务问题": [
            "NodeService", "NodeAuthCheckService", "EemCloudAuthService",
            "QuantityObjectDao", "EnergySupplyDao", "AuthUtils"
        ],
        "工具类问题": [
            "CommonUtils", "JsonUtil", "DateUtils", "MessagePushUtils"
        ],
        "权限相关问题": [
            "permission", "auth", "Permission", "Auth"
        ],
        "注解问题": [
            "@Resource", "@Autowired", "@Component", "@Service"
        ]
    }
    
    for file_path, file_errors in grouped_errors.items():
        for error in file_errors:
            categorized_flag = False
            
            # 根据知识库模式进行分类
            for category, patterns in knowledge_patterns.items():
                for pattern in patterns:
                    if (pattern.lower() in error.description.lower() or 
                        pattern.lower() in error.highlighted_element.lower()):
                        categorized[category].append({
                            "file": file_path,
                            "error": error,
                            "matched_pattern": pattern
                        })
                        categorized_flag = True
                        break
                if categorized_flag:
                    break
            
            # 如果没有匹配到任何模式，归类为未识别
            if not categorized_flag:
                categorized["未识别问题"].append({
                    "file": file_path,
                    "error": error,
                    "matched_pattern": "无匹配"
                })
    
    return categorized

def generate_task_list(categorized_errors):
    """生成task.md处理任务列表"""
    with open("task.md", "w", encoding="utf-8") as f:
        f.write("# Java代码迁移处理任务列表\n\n")
        f.write("## 任务执行顺序\n\n")
        f.write("按照从底层到上层的顺序执行：常量类 → 实体类 → 工具类 → DAO → Service → Controller\n\n")
        
        # 按优先级排序的任务类别
        priority_order = [
            ("import问题", "🟢", "高优先级 - 基础依赖问题"),
            ("返回类型问题", "🟡", "中优先级 - 接口统一"),
            ("工具类问题", "🟡", "中优先级 - 工具类替换"),
            ("注解问题", "🟡", "中优先级 - 依赖注入"),
            ("权限相关问题", "🟡", "中优先级 - 权限系统"),
            ("废弃服务问题", "🔴", "低优先级 - 复杂重构"),
            ("未识别问题", "⚪", "待分析 - 需要人工判断")
        ]
        
        task_counter = 1
        
        for category, color, description in priority_order:
            if category in categorized_errors and categorized_errors[category]:
                f.write(f"## {task_counter}. {category} {color}\n\n")
                f.write(f"**描述**: {description}\n\n")
                f.write(f"**错误数量**: {len(categorized_errors[category])}\n\n")
                
                # 按文件分组显示任务
                file_groups = {}
                for item in categorized_errors[category]:
                    file_path = item["file"]
                    if file_path not in file_groups:
                        file_groups[file_path] = []
                    file_groups[file_path].append(item)
                
                subtask_counter = 1
                for file_path, items in file_groups.items():
                    f.write(f"### {task_counter}.{subtask_counter} 处理文件: `{file_path}`\n\n")
                    
                    # 根据错误类型提供具体的解决方案
                    if category == "import问题":
                        write_import_solutions(f, items)
                    elif category == "返回类型问题":
                        write_return_type_solutions(f, items)
                    elif category == "工具类问题":
                        write_util_class_solutions(f, items)
                    elif category == "废弃服务问题":
                        write_deprecated_service_solutions(f, items)
                    elif category == "注解问题":
                        write_annotation_solutions(f, items)
                    elif category == "权限相关问题":
                        write_permission_solutions(f, items)
                    else:
                        write_unrecognized_solutions(f, items)
                    
                    f.write(f"\n**验证步骤**:\n")
                    f.write(f"1. 修改完成后编译检查: `mvn compile`\n")
                    f.write(f"2. 运行相关测试\n")
                    f.write(f"3. 提交代码: `git add . && git commit -m \"fix: 修复{file_path}中的{category}\"`\n\n")
                    
                    subtask_counter += 1
                
                task_counter += 1
                f.write("---\n\n")

def write_import_solutions(f, items):
    """写入import问题的解决方案"""
    f.write("**解决方案**:\n\n")
    
    # 常见的import替换映射
    import_mappings = {
        "EnumAndOr": "com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr",
        "OperationPermission": "com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission",
        "OperationLog": "com.cet.eem.fusion.config.sdk.service.log.OperationLog",
        "Result": "com.cet.electric.commons.ApiResult",
        "ResultWithTotal": "com.cet.electric.commons.ApiResult"
    }
    
    for item in items:
        error = item["error"]
        element = error.highlighted_element
        
        if element in import_mappings:
            f.write(f"🟢 **{element}** (行 {error.line}): 直接替换\n")
            f.write(f"```java\n")
            f.write(f"import {import_mappings[element]};\n")
            f.write(f"```\n\n")
        else:
            f.write(f"🟡 **{element}** (行 {error.line}): 需要查找替换\n")
            f.write(f"- 使用 `class_name_finder.py` 查找: `python java_error_analyzer/class_name_finder.py {element}`\n")
            f.write(f"- 如果找到多个候选，使用 `fuzzy_matcher.py` 进行模糊匹配\n\n")

def write_return_type_solutions(f, items):
    """写入返回类型问题的解决方案"""
    f.write("**解决方案**:\n\n")
    f.write("统一替换为 `ApiResult<T>` 类型：\n\n")
    
    for item in items:
        error = item["error"]
        f.write(f"- 行 {error.line}: `{error.highlighted_element}`\n")
    
    f.write("\n**替换步骤**:\n")
    f.write("1. 更新import语句: `import com.cet.electric.commons.ApiResult;`\n")
    f.write("2. 替换返回类型: `Result<T>` → `ApiResult<T>`\n")
    f.write("3. 更新方法调用: `Result.ok(data)` → `ApiResult.newResult(200, \"success\", data)`\n\n")

def write_util_class_solutions(f, items):
    """写入工具类问题的解决方案"""
    f.write("**解决方案**:\n\n")
    
    util_mappings = {
        "CommonUtils": "使用 NumberCalcUtils 或 SortUtils 替换",
        "JsonUtil": "使用 JsonTransferUtils 替换",
        "DateUtils": "使用 Java 8 时间API替换",
        "MessagePushUtils": "使用 WebNotification 替换"
    }
    
    for item in items:
        error = item["error"]
        element = error.highlighted_element
        
        for util_class, solution in util_mappings.items():
            if util_class in element:
                f.write(f"🟡 **{element}** (行 {error.line}): {solution}\n")
                break
        else:
            f.write(f"⚪ **{element}** (行 {error.line}): 需要人工分析\n")
    
    f.write("\n")

def write_deprecated_service_solutions(f, items):
    """写入废弃服务问题的解决方案"""
    f.write("**解决方案**:\n\n")
    f.write("🔴 **注意**: 这些是复杂的重构任务，建议最后处理\n\n")
    
    for item in items:
        error = item["error"]
        f.write(f"- 行 {error.line}: `{error.highlighted_element}` - 需要根据知识库进行重构\n")
    
    f.write("\n")

def write_annotation_solutions(f, items):
    """写入注解问题的解决方案"""
    f.write("**解决方案**:\n\n")
    f.write("检查和更新依赖注入注解：\n\n")
    
    for item in items:
        error = item["error"]
        f.write(f"- 行 {error.line}: 检查 `@Resource` 是否需要添加插件前缀名称\n")
    
    f.write("\n")

def write_permission_solutions(f, items):
    """写入权限相关问题的解决方案"""
    f.write("**解决方案**:\n\n")
    f.write("更新权限相关代码：\n\n")
    
    for item in items:
        error = item["error"]
        f.write(f"- 行 {error.line}: 检查权限ID是否需要加10000\n")
    
    f.write("\n")

def write_unrecognized_solutions(f, items):
    """写入未识别问题的解决方案"""
    f.write("**解决方案**:\n\n")
    f.write("⚪ **需要人工分析**: 这些错误未能自动分类，需要开发人员手动检查\n\n")
    
    for item in items:
        error = item["error"]
        f.write(f"- 行 {error.line}: `{error.highlighted_element}` - {error.description}\n")
    
    f.write("\n")

if __name__ == "__main__":
    main()