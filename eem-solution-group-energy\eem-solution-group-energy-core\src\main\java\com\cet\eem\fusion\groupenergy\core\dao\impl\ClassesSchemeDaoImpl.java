package com.cet.eem.fusion.groupenergy.core.dao.impl;

import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.cet.eem.fusion.groupenergy.core.dao.ClassesSchemeDao;
import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 班次方案dao
 *
 * <AUTHOR>
 */
@Component
public class ClassesSchemeDaoImpl extends ModelDaoImpl<ClassesScheme> implements ClassesSchemeDao {

    /**
     * 根据id查询班次方案
     *
     * @param id 班次方案id
     * @return 班次方案
     */
    @Override
    public ClassesScheme queryById(Long id) {
        if (Objects.isNull(id)){
            return null;
        }

        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(TableNameDef.CLASSES_SCHEME)
                .eq(TableColumnNameDef.COLUMN_ID, id)
                .leftJoin(TableNameDef.CLASSES_CONFIG);

        List<ClassesScheme> classesSchemeList = modelServiceUtils.queryWithRedis(builder, ClassesScheme.class);

        if (CollectionUtils.isNotEmpty(classesSchemeList)) {
            return classesSchemeList.get(0);
        }
        return null;
    }
}
