# Git提交助手脚本
# 用于批量代码编写后的git提交操作

param(
    [Parameter(Mandatory=$false)]
    [string]$CommitMessage = "",

    [Parameter(Mandatory=$false)]
    [switch]$Interactive,

    [Parameter(Mandatory=$false)]
    [switch]$ShowStatus
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 检查是否在git仓库中
function Test-GitRepository {
    try {
        git rev-parse --git-dir 2>$null | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 获取当前分支名
function Get-CurrentBranch {
    try {
        return git branch --show-current
    }
    catch {
        return "unknown"
    }
}

# 显示git状态
function Show-GitStatus {
    Write-ColorOutput "=== Git 状态 ===" "Cyan"
    git status --short
    Write-Host ""
}

# 获取提交信息
function Get-CommitMessage {
    param([string]$DefaultMessage)
    
    if ($DefaultMessage -ne "") {
        return $DefaultMessage
    }
    
    Write-ColorOutput "请输入提交信息 (或按Enter使用默认信息):" "Yellow"
    Write-ColorOutput "默认: 'AI助手处理: 代码优化和功能实现'" "Gray"
    
    $userInput = Read-Host "提交信息"
    
    if ([string]::IsNullOrWhiteSpace($userInput)) {
        return "AI助手处理: 代码优化和功能实现"
    }
    
    return $userInput
}

# 主函数
function Main {
    Write-ColorOutput "=== Git 提交助手 ===" "Green"
    Write-Host ""
    
    # 检查是否在git仓库中
    if (-not (Test-GitRepository)) {
        Write-ColorOutput "错误: 当前目录不是git仓库!" "Red"
        exit 1
    }
    
    # 显示当前分支
    $currentBranch = Get-CurrentBranch
    Write-ColorOutput "当前分支: $currentBranch" "Cyan"
    Write-Host ""
    
    # 显示git状态
    if ($ShowStatus -or -not $PSBoundParameters.ContainsKey('ShowStatus')) {
        Show-GitStatus
    }
    
    # 检查是否有变更
    $hasChanges = git diff --cached --quiet; $? -eq $false
    $hasUnstagedChanges = git diff --quiet; $? -eq $false
    
    if (-not $hasChanges -and -not $hasUnstagedChanges) {
        Write-ColorOutput "没有检测到任何变更，无需提交。" "Yellow"
        exit 0
    }
    
    # 如果有未暂存的变更，询问是否添加
    if ($hasUnstagedChanges) {
        if ($Interactive -or -not $PSBoundParameters.ContainsKey('Interactive')) {
            Write-ColorOutput "检测到未暂存的变更。" "Yellow"
            $addAll = Read-Host "是否添加所有变更到暂存区? (y/N)"
            
            if ($addAll -eq "y" -or $addAll -eq "Y") {
                Write-ColorOutput "添加所有变更到暂存区..." "Green"
                git add .
            }
            else {
                Write-ColorOutput "请手动添加需要提交的文件到暂存区。" "Yellow"
                Write-ColorOutput "使用: git add <文件名> 或 git add . (添加所有)" "Gray"
                exit 0
            }
        }
        else {
            Write-ColorOutput "自动添加所有变更到暂存区..." "Green"
            git add .
        }
    }
    
    # 获取提交信息
    $commitMsg = Get-CommitMessage $CommitMessage
    
    # 显示即将提交的内容
    Write-ColorOutput "=== 即将提交的变更 ===" "Cyan"
    git diff --cached --stat
    Write-Host ""
    
    # 确认提交
    if ($Interactive -or -not $PSBoundParameters.ContainsKey('Interactive')) {
        Write-ColorOutput "提交信息: $commitMsg" "Yellow"
        $confirm = Read-Host "确认提交? (Y/n)"
        
        if ($confirm -eq "n" -or $confirm -eq "N") {
            Write-ColorOutput "取消提交。" "Yellow"
            exit 0
        }
    }
    
    # 执行提交
    Write-ColorOutput "正在提交..." "Green"
    try {
        git commit -m $commitMsg
        Write-ColorOutput "提交成功!" "Green"
    }
    catch {
        Write-ColorOutput "提交失败: $_" "Red"
        exit 1
    }
    
    # 提交完成提示
    Write-ColorOutput "代码已成功提交到本地仓库！" "Green"
    Write-ColorOutput "提交信息: $commitMsg" "Cyan"
    
    Write-Host ""
    Write-ColorOutput "=== 操作完成 ===" "Green"
}

# 执行主函数
Main
