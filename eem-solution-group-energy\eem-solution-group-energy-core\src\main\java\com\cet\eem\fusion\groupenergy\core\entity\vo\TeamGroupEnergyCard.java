package com.cet.eem.fusion.groupenergy.core.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班组能耗卡片数据
 *
 * <AUTHOR>
 */
@ApiModel(value = "TeamGroupEnergyCard", description = "班组能耗卡片数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeamGroupEnergyCard {
    @ApiModelProperty(value = "班组id")
    private Long teamGroupId;

    @ApiModelProperty(value = "班组名称")
    private String teamGroupName;

    @ApiModelProperty(value = "用能占比")
    private Double energyProportion;

    @ApiModelProperty(value = "总能耗")
    private Double energyCount;

    @ApiModelProperty(value = "总班次")
    private Integer classesTotal;

    @ApiModelProperty(value = "平均班次能耗")
    private Double avgEnergy;

}
