package com.cet.eem.fusion.groupenergy.core.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 排班配置VO
 *
 * <AUTHOR>
 */
@ApiModel(value = "SchedulingClassesConfigVO", description = "排班配置VO")
@Data
public class SchedulingClassesConfigVO {

    @ApiModelProperty(value = "班次id")
    private Long classesConfigId;

    @ApiModelProperty(value = "班次次序")
    private Integer order;

    @ApiModelProperty(value = "班次名称")
    private String classesConfigName;

    @ApiModelProperty(value = "班次开始时间")
    private Long startTime;

    @ApiModelProperty(value = "班次结束时间")
    private Long endTime;

    @ApiModelProperty(value = "班组id")
    private Long teamGroupId;

    @ApiModelProperty(value = "班组名称")
    private String teamGroupName;

    @ApiModelProperty(value = "班组人数")
    private Integer personCount;

    @ApiModelProperty(value = "班组成员json")
    private String personId;

    @ApiModelProperty(value = "班组颜色标识")
    private String color;
}
