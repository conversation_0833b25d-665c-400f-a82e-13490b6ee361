"""
配置管理器实现

负责配置文件的加载、保存和验证。
基于error_prone_json_reporter的配置管理器，适配源码上下文提取器的需求。
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path

from .models import ExtractorConfig
# Interface removed for simplicity


class ConfigManager:
    """配置管理器实现"""
    
    def __init__(self):
        self.default_config_path = "source_context_extractor/config.yaml"
        self.env_prefix = "SOURCE_EXTRACTOR_"  # 环境变量前缀
    
    def load_config(self, config_path: str = None) -> ExtractorConfig:
        """
        加载配置文件，支持环境变量覆盖
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
            
        Returns:
            配置对象
        """
        if config_path is None:
            config_path = self.default_config_path
        
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(config_path):
            config = self._create_default_config()
        else:
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                # 将YAML数据转换为ExtractorConfig对象
                config = self._dict_to_config(config_data)
                
            except Exception as e:
                raise ValueError(f"Failed to load config from {config_path}: {e}")
        
        # 应用环境变量覆盖
        config = self._apply_env_overrides(config)
        
        # 验证配置
        config.validate()
        return config
    
    def save_config(self, config: ExtractorConfig, config_path: str) -> None:
        """
        保存配置文件
        
        Args:
            config: 配置对象
            config_path: 配置文件路径
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            # 将ExtractorConfig对象转换为字典
            config_dict = self._config_to_dict(config)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                         
        except Exception as e:
            raise ValueError(f"Failed to save config to {config_path}: {e}")
    
    def merge_cli_args(self, config: ExtractorConfig, cli_args: Dict[str, Any]) -> ExtractorConfig:
        """
        合并命令行参数到配置
        
        Args:
            config: 基础配置
            cli_args: 命令行参数
            
        Returns:
            合并后的配置
        """
        # 创建配置副本
        merged_config = ExtractorConfig(**config.__dict__)
        
        # 合并命令行参数
        for key, value in cli_args.items():
            if value is not None and hasattr(merged_config, key):
                setattr(merged_config, key, value)
        
        merged_config.validate()
        return merged_config
    
    def _create_default_config(self) -> ExtractorConfig:
        """创建默认配置"""
        return ExtractorConfig()
    
    def _dict_to_config(self, config_data: Dict[str, Any]) -> ExtractorConfig:
        """将字典转换为ExtractorConfig对象"""
        paths = config_data.get('paths', {})
        processing = config_data.get('processing', {})
        output = config_data.get('output', {})
        logging = config_data.get('logging', {})
        
        return ExtractorConfig(
            # 路径配置
            src_path=paths.get('src_path', 'newcode'),
            legacy_src_path=paths.get('legacy_src_path', 'oldcode'),
            knowledge_base_path=paths.get('knowledge_base_path', '知识库'),
            
            # 处理配置
            enable_ast_parsing=processing.get('enable_ast_parsing', True),
            enable_text_fallback=processing.get('enable_text_fallback', True),
            max_context_lines=processing.get('max_context_lines', 10),
            timeout_seconds=processing.get('timeout_seconds', 30),
            
            # 输出配置
            output_path=output.get('output_path', 'source_context_analysis.md'),
            
            # 日志配置
            log_level=logging.get('level', 'INFO'),
            log_format=logging.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            console_output=logging.get('console', True),
            file_output=logging.get('file', True),
            log_file=logging.get('log_file', 'logs/source_context_extractor.log')
        )
    
    def _config_to_dict(self, config: ExtractorConfig) -> Dict[str, Any]:
        """将ExtractorConfig对象转换为字典"""
        return {
            'paths': {
                'src_path': config.src_path,
                'legacy_src_path': config.legacy_src_path,
                'knowledge_base_path': config.knowledge_base_path
            },
            'processing': {
                'enable_ast_parsing': config.enable_ast_parsing,
                'enable_text_fallback': config.enable_text_fallback,
                'max_context_lines': config.max_context_lines,
                'timeout_seconds': config.timeout_seconds
            },
            'output': {
                'output_path': config.output_path
            },
            'logging': {
                'level': config.log_level,
                'format': config.log_format,
                'console': config.console_output,
                'file': config.file_output,
                'log_file': config.log_file
            }
        }
    
    def _apply_env_overrides(self, config: ExtractorConfig) -> ExtractorConfig:
        """
        应用环境变量覆盖配置
        
        环境变量命名规则：SOURCE_EXTRACTOR_<SECTION>_<KEY>
        例如：SOURCE_EXTRACTOR_PATHS_SRC_PATH
        
        Args:
            config: 基础配置对象
            
        Returns:
            应用环境变量覆盖后的配置对象
        """
        # 环境变量映射表
        env_mappings = {
            # 路径配置
            f"{self.env_prefix}PATHS_SRC_PATH": "src_path",
            f"{self.env_prefix}PATHS_LEGACY_SRC_PATH": "legacy_src_path",
            f"{self.env_prefix}PATHS_KNOWLEDGE_BASE_PATH": "knowledge_base_path",
            
            # 处理配置
            f"{self.env_prefix}PROCESSING_ENABLE_AST_PARSING": "enable_ast_parsing",
            f"{self.env_prefix}PROCESSING_ENABLE_TEXT_FALLBACK": "enable_text_fallback",
            f"{self.env_prefix}PROCESSING_MAX_CONTEXT_LINES": "max_context_lines",
            f"{self.env_prefix}PROCESSING_TIMEOUT_SECONDS": "timeout_seconds",
            
            # 输出配置
            f"{self.env_prefix}OUTPUT_PATH": "output_path",
            
            # 日志配置
            f"{self.env_prefix}LOGGING_LEVEL": "log_level",
            f"{self.env_prefix}LOGGING_FORMAT": "log_format",
            f"{self.env_prefix}LOGGING_CONSOLE": "console_output",
            f"{self.env_prefix}LOGGING_FILE": "file_output",
            f"{self.env_prefix}LOGGING_LOG_FILE": "log_file"
        }
        
        # 创建配置副本
        overridden_config = ExtractorConfig(**config.__dict__)
        
        # 应用环境变量覆盖
        for env_var, config_attr in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # 类型转换
                converted_value = self._convert_env_value(env_value, config_attr)
                setattr(overridden_config, config_attr, converted_value)
        
        return overridden_config
    
    def _convert_env_value(self, env_value: str, config_attr: str) -> Any:
        """
        转换环境变量值为适当的类型
        
        Args:
            env_value: 环境变量值
            config_attr: 配置属性名
            
        Returns:
            转换后的值
        """
        # 布尔类型字段
        bool_fields = {
            "enable_ast_parsing", "enable_text_fallback", "console_output", "file_output"
        }
        
        # 整数类型字段
        int_fields = {
            "max_context_lines", "timeout_seconds"
        }
        
        if config_attr in bool_fields:
            return env_value.lower() in ("true", "1", "yes", "on")
        elif config_attr in int_fields:
            try:
                return int(env_value)
            except ValueError:
                raise ValueError(f"Invalid integer value for {config_attr}: {env_value}")
        else:
            return env_value