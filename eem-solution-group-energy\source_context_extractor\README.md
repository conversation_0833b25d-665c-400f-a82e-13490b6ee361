# 源码上下文提取器

一个用于分析Java代码迁移错误的工具，能够从错误报告中提取详细的源码信息，包括方法定义、上下文和注释，并生成结构化的分析文档。

## 功能特性

- **JSON错误报告解析**: 解析类似target_method_test.json结构的错误报告
- **智能文件定位**: 在新框架和迁移前代码中定位源文件
- **AST语法分析**: 使用javalang进行精确的Java代码解析
- **上下文提取**: 提取完整的方法源码、注释和上下文信息
- **结构化输出**: 生成包含指定JSON格式的Markdown分析报告
- **错误处理**: 完善的异常处理和错误恢复机制
- **配置管理**: 灵活的配置文件和命令行参数支持

## 安装

1. 安装依赖包：
```bash
pip install -r requirements.txt
```

2. 验证安装：
```bash
python -m source_context_extractor.main --version
```

## 使用方法

### 基本用法

```bash
# 分析错误报告文件
python -m source_context_extractor.main -i target_method_test.json

# 指定输出文件
python -m source_context_extractor.main -i errors.json -o analysis.md

# 指定源码路径
python -m source_context_extractor.main -i errors.json --src-path newcode --legacy-src-path oldcode
```

### 配置文件

创建配置文件 `config.yaml`：

```yaml
paths:
  src_path: "newcode"
  legacy_src_path: "oldcode"
  knowledge_base_path: "知识库"

processing:
  enable_ast_parsing: true
  enable_text_fallback: true
  max_context_lines: 10
  timeout_seconds: 30

output:
  output_path: "source_context_analysis.md"

logging:
  level: "INFO"
  console: true
  file: true
  log_file: "logs/source_context_extractor.log"
```

使用配置文件：
```bash
python -m source_context_extractor.main -i errors.json -c config.yaml
```

### 命令行参数

```bash
python -m source_context_extractor.main -h
```

主要参数：
- `-i, --input`: 输入的JSON错误报告文件路径（必需）
- `-o, --output`: 输出的Markdown分析报告文件路径
- `--src-path`: 新框架源码路径
- `--legacy-src-path`: 迁移前源码路径
- `--disable-ast`: 禁用AST解析
- `--max-context-lines`: 最大上下文行数
- `-c, --config`: 配置文件路径
- `--log-level`: 日志级别
- `--dry-run`: 试运行模式

## 输入格式

输入的JSON文件应包含错误项数组，每个错误项包含以下字段：

```json
[
  {
    "package": "com.example.service.impl",
    "class": "UserServiceImpl",
    "missing_method": "getUserById",
    "in_param": {"id": "java.lang.Long"},
    "out_return": "com.example.model.User",
    "line": [123],
    "context": "Method call in service implementation",
    "method_signature": "getUserById(id: java.lang.Long) -> com.example.model.User",
    "location": {
      "file": "/src/main/java/com/example/service/impl/UserServiceImpl.java",
      "line": 123,
      "column": 25
    }
  }
]
```

## 输出格式

生成的Markdown报告包含每个方法的分析结果：

```markdown
# 源码上下文分析报告

## 方法分析结果

### 1. getUserById 方法

```json
{
  "missing_method": "getUserById",
  "in_param": {"id": "java.lang.Long"},
  "out_return": "com.example.model.User",
  "context": "Method call in service implementation",
  "content": "public User getUserById(Long id) { ... }",
  "notes": "根据用户ID获取用户信息的方法"
}
```
```

## 项目结构

```
source_context_extractor/
├── __init__.py              # 包初始化
├── main.py                  # 主程序入口
├── models.py                # 数据模型定义
├── interfaces.py            # 接口定义
├── config.py                # 配置管理
├── logging_config.py        # 日志配置
├── error_handling.py        # 错误处理
├── config.yaml              # 默认配置文件
├── requirements.txt         # 依赖包列表
└── README.md               # 项目说明
```

## 开发计划

该项目基于规范化的任务列表开发，主要包括：

1. ✅ 创建项目结构和基础框架
2. ⏳ 实现JSON输入解析器
3. ⏳ 实现错误位置解析器
4. ⏳ 实现迁移前源码搜索器
5. ⏳ 实现AST解析和方法提取
6. ⏳ 实现上下文分析器
7. ⏳ 实现输出格式化器
8. ⏳ 实现主程序和批量处理
9. ⏳ 实现配置管理和异常处理
10. ⏳ 实现日志记录和测试验证

## 许可证

本项目采用MIT许可证。