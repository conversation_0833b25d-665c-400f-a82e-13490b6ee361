# 04 权限相关

## 鉴权/查询权限操作

1.  提供鉴权、查询权限资源的功能：NodeAuthCheckService
    
2.  方法签名如下：
    

1.  检查用户对组织节点的完整权限，即对管理层级、管网层级的节点权限校验：boolean checkCompleteOrganizationNodes(@NotNull ParentParam parentParam, List<? extends BaseEntity> nodes)；
    
2.  检查用户对层级的根节点是否具有权限：boolean checkCompleteOrganizationNodes(@NotNull ParentParam parentParam)；
    
3.  检查用户对组织节点的完整权限，即对管理层级、管网层级的节点权限校验：boolean checkCompleteAuth(Long userId, Long tenantId, @NotNull Long resourceType, @NotEmpty List<? extends BaseEntity> nodes)；
    
4.  检查用户对组织节点的完整权限：boolean checkCompleteAuth(@NotNull Long userId, Long tenantId, @NotNull Long resourceType, BaseEntity node)；
    
5.  检查用户对组织节点的完整权限：boolean checkCompleteAuth(@NotNull Long userId, Long tenantId, @NotNull Long resourceType, Long projectId)；
    
6.  检查用户对组织节点的完整权限：boolean checkCompleteAuth(@NotNull ParentParam parentParam, @NotNull Long resourceType, Long projectId)；
    
7.  检查用户对维度节点的完整权限：boolean checkCompleteDimNodes(@NotNull ParentParam parentParam, List<? extends BaseEntity> nodes)；
    
8.  查询节点拥有通知权限的用户：List<NodeNoticeAuthVo> queryNodeNoticeAuth(Collection<BaseVo> nodes, ResourceDefineVO noticeResourceDefineVO, Long tenantId)；
    
9.  查询用户资源节点：List<ResourceDefinedDO> queryUserResourceNode(Long userId, Long resourceType)；
    

## 通知权限接口（BaseEemNoticeController）

1.  方法列表如下：
    
    1.  查询节点可以通知的用户：public ApiResult<List<NodeNoticeAuthVo>> queryNodeNoticeAuth(@NotNull @RequestHeader(ColumnDef.TENANT\_HEADER) Long tenantId, @RequestBody @ApiParam(name = "nodes", value = "设备id", required = true) NoticeQueryVo queryVo)
        
    2.  查询节点可以通知的用户，用于能耗、能效、损耗等通知：public ApiResult<List<NodeNoticeAuthVo>> queryNodeNoticeAuth(@NotNull @RequestHeader(ColumnDef.TENANT\_HEADER) Long tenantId, @RequestBody @ApiParam(name = "nodes", value = "设备id", required = true) List<BaseVo> nodes)