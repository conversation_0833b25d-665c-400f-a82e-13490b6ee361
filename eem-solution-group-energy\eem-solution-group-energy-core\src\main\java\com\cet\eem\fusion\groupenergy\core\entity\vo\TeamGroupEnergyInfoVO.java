package com.cet.eem.fusion.groupenergy.core.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 班组用能信息VO
 *
 * <AUTHOR>
 */
@ApiModel(value = "TeamGroupEnergyInfoVO", description = "班组用能信息VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeamGroupEnergyInfoVO {

    @ApiModelProperty(value = "能耗单位")
    private String energyUnit;

    @ApiModelProperty(value = "班组总能耗")
    private Double energyTotal;

    @ApiModelProperty(value = "总班次")
    private Integer classesTotal;

    @ApiModelProperty(value = "平均班次能耗")
    private Double avgEnergy;

    @ApiModelProperty(value = "班组能耗看板数据")
    private List<TeamGroupEnergyCard> teamGroupEnergyCardList;


}
