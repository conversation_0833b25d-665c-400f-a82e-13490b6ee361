package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 排班方案关联假期dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "SchedulingSchemeRelatedHolidayDTO", description = "排班方案关联假期dto")
@Data
public class SchedulingSchemeRelatedHolidayDTO {

    @ApiModelProperty(value = "方案id", required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "假期", required = true)
    private List<Long> holidayList;
}
