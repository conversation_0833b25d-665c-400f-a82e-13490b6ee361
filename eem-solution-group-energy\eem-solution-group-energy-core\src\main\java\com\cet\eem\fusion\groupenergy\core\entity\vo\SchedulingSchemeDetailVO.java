package com.cet.eem.fusion.groupenergy.core.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 排班方案详情vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "SchedulingSchemeDetailVO", description = "排班方案详情vo")
public class SchedulingSchemeDetailVO {

    @ApiModelProperty(value = "方案id")
    private Long id;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "班组类型（1-运维，2-生产）")
    private Integer classTeamType;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "创建人id")
    private Long operator;

    @ApiModelProperty(value = "班次方案")
    private List<ClassesSchemeVO> classesSchemeList;

    @ApiModelProperty(value = "班组")
    private List<TeamGroupInfoVO> teamGroupInfoList;


}
