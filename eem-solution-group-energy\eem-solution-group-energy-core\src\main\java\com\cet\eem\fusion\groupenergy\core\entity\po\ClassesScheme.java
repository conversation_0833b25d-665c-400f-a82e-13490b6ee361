package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 班次方案表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:25
 */
@Data
@ModelLabel(ModelLabelDef.CLASSES_SCHEME)
public class ClassesScheme extends EntityWithName {

    @ApiModelProperty("班次配置")
    private List<ClassesConfig> classesconfig_model;

    public ClassesScheme() {
        this.modelLabel = ModelLabelDef.CLASSES_SCHEME;
    }
}