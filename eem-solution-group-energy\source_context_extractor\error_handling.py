"""
错误处理工具

提供统一的错误处理、异常管理和错误恢复机制。
基于error_prone_json_reporter的错误处理系统。
"""

import logging
from typing import Dict, Any, Optional, Callable
from contextlib import contextmanager


class SourceExtractorError(Exception):
    """源码上下文提取器基础异常"""
    pass


class FileAccessError(SourceExtractorError):
    """文件访问异常"""
    pass


class SourceFileNotFoundError(FileAccessError):
    """源文件未找到异常"""
    pass


class ParseError(SourceExtractorError):
    """解析异常基类"""
    pass


class FileLocatorError(SourceExtractorError):
    """文件定位器异常"""
    pass


class ASTParseError(ParseError):
    """AST解析异常"""
    pass


class JSONParseError(ParseError):
    """JSON解析异常"""
    pass


class ConfigurationError(SourceExtractorError):
    """配置错误异常"""
    pass


class TimeoutError(SourceExtractorError):
    """超时异常"""
    pass


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.error_counts = {}
        self.max_recoverable_errors = 10
    
    def handle_error(self, error: Exception, context: str = "", 
                    recoverable: bool = True) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            recoverable: 是否为可恢复的错误
            
        Returns:
            是否应该继续执行
        """
        error_type = type(error).__name__
        
        # 统计错误次数
        if error_type not in self.error_counts:
            self.error_counts[error_type] = 0
        self.error_counts[error_type] += 1
        
        # 构建错误消息
        error_msg = f"{context}: {error}" if context else str(error)
        
        if recoverable:
            self.logger.warning(f"可恢复错误 ({error_type}): {error_msg}")
            
            # 如果同类型错误过多，建议停止
            if self.error_counts[error_type] > self.max_recoverable_errors:
                self.logger.error(f"{error_type} 错误过多 ({self.error_counts[error_type]} 次)，建议检查配置")
                return False
            
            return True
        else:
            self.logger.error(f"不可恢复错误 ({error_type}): {error_msg}")
            return False
    
    def get_error_summary(self) -> Dict[str, int]:
        """获取错误统计摘要"""
        return self.error_counts.copy()
    
    def log_error_summary(self):
        """记录错误统计摘要"""
        if not self.error_counts:
            self.logger.info("执行过程中未发生错误")
            return
        
        self.logger.info("错误统计摘要:")
        total_errors = sum(self.error_counts.values())
        for error_type, count in self.error_counts.items():
            percentage = (count / total_errors) * 100
            self.logger.info(f"  {error_type}: {count} 次 ({percentage:.1f}%)")
        self.logger.info(f"  总计: {total_errors} 次错误")
    
    def reset_error_counts(self):
        """重置错误计数"""
        self.error_counts.clear()


@contextmanager
def safe_execution(error_handler: ErrorHandler, operation_name: str, 
                  recoverable: bool = True, default_return=None):
    """
    安全执行上下文管理器
    
    Args:
        error_handler: 错误处理器
        operation_name: 操作名称
        recoverable: 是否为可恢复的错误
        default_return: 发生错误时的默认返回值
    """
    try:
        yield
    except Exception as e:
        should_continue = error_handler.handle_error(e, operation_name, recoverable)
        if not should_continue and not recoverable:
            raise
        return default_return


def retry_on_error(max_retries: int = 3, delay: float = 1.0, 
                  backoff_factor: float = 2.0, 
                  exceptions: tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            import time
            
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        raise last_exception
            
            return None
        return wrapper
    return decorator


def validate_file_access(file_path: str, operation: str = "访问") -> None:
    """
    验证文件访问权限
    
    Args:
        file_path: 文件路径
        operation: 操作类型
        
    Raises:
        FileAccessError: 文件访问错误
    """
    import os
    
    if not os.path.exists(file_path):
        raise SourceFileNotFoundError(f"文件不存在: {file_path}")
    
    if operation == "读取" and not os.access(file_path, os.R_OK):
        raise FileAccessError(f"文件不可读: {file_path}")
    
    if operation == "写入" and not os.access(file_path, os.W_OK):
        raise FileAccessError(f"文件不可写: {file_path}")


def validate_directory_access(dir_path: str, create_if_missing: bool = False) -> None:
    """
    验证目录访问权限
    
    Args:
        dir_path: 目录路径
        create_if_missing: 如果目录不存在是否创建
        
    Raises:
        FileAccessError: 目录访问错误
    """
    import os
    from pathlib import Path
    
    if not os.path.exists(dir_path):
        if create_if_missing:
            try:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise FileAccessError(f"无法创建目录 {dir_path}: {e}")
        else:
            raise FileAccessError(f"目录不存在: {dir_path}")
    
    if not os.path.isdir(dir_path):
        raise FileAccessError(f"路径不是目录: {dir_path}")
    
    if not os.access(dir_path, os.R_OK):
        raise FileAccessError(f"目录不可读: {dir_path}")


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.recovery_strategies = {}
    
    def register_recovery_strategy(self, error_type: type, strategy: Callable):
        """
        注册错误恢复策略
        
        Args:
            error_type: 错误类型
            strategy: 恢复策略函数
        """
        self.recovery_strategies[error_type] = strategy
    
    def attempt_recovery(self, error: Exception, context: Dict[str, Any]) -> Optional[Any]:
        """
        尝试错误恢复
        
        Args:
            error: 异常对象
            context: 上下文信息
            
        Returns:
            恢复结果或None
        """
        error_type = type(error)
        
        if error_type in self.recovery_strategies:
            try:
                self.logger.info(f"尝试恢复错误: {error_type.__name__}")
                result = self.recovery_strategies[error_type](error, context)
                self.logger.info("错误恢复成功")
                return result
            except Exception as recovery_error:
                self.logger.error(f"错误恢复失败: {recovery_error}")
        
        return None


# 预定义的恢复策略
def ast_parse_fallback_strategy(error: ASTParseError, context: Dict[str, Any]) -> Optional[str]:
    """AST解析失败的回退策略"""
    # 返回使用文本解析的标识
    return "use_text_parsing"


def file_not_found_search_strategy(error: SourceFileNotFoundError, context: Dict[str, Any]) -> Optional[str]:
    """文件未找到的搜索策略"""
    # 在多个可能的路径中搜索文件
    search_paths = context.get('search_paths', [])
    file_name = context.get('file_name', '')
    
    if not search_paths or not file_name:
        return None
    
    import os
    from pathlib import Path
    
    for search_path in search_paths:
        for root, dirs, files in os.walk(search_path):
            if file_name in files:
                found_path = os.path.join(root, file_name)
                return found_path
    
    return None