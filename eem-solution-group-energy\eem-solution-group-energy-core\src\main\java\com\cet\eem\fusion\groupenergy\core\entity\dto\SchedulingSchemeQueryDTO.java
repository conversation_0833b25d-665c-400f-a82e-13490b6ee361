package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询排班方案dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "SchedulingSchemeQueryDTO", description = "查询排班方案dto")
@Data
public class SchedulingSchemeQueryDTO {

    @ApiModelProperty(value = "关键字", required = false)
    private String key;

    @ApiModelProperty(value = "方案类型（1-运维，2-生产）", required = false)
    private Integer classTeamType;

    @ApiModelProperty(value = "页码", required = true)
    private Integer index;

    @ApiModelProperty(value = "数量", required = true)
    private Integer size;

}
