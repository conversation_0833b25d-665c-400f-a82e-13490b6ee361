package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 班组能耗表
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.TEAM_GROUP_ENERGY)
public class TeamGroupEnergy extends EntityWithName {

    @ApiModelProperty("节点id")
    @JsonProperty("objectid")
    private Long objectId;

    @ApiModelProperty("节点类型")
    @JsonProperty("objectlabel")
    private String objectLabel;

    @ApiModelProperty("能源类型")
    @JsonProperty("energytype")
    private Integer energyType;

    @ApiModelProperty("排班日期")
    @JsonProperty("logtime")
    private Long logTime;

    @ApiModelProperty("聚合周期")
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    @ApiModelProperty("班次id")
    @JsonProperty("classesid")
    private Long classesId;

    @ApiModelProperty("班组id")
    @JsonProperty("teamgroupid")
    private Long teamGroupId;

    @ApiModelProperty("能耗值")
    private Double value;

    public TeamGroupEnergy() {
        this.modelLabel = ModelLabelDef.TEAM_GROUP_ENERGY;
    }
}
