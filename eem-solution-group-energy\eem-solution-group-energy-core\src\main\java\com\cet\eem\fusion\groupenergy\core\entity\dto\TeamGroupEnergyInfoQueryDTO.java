package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 班组用能查询条件
 *
 * <AUTHOR>
 */
@ApiModel(value = "TeamGroupEnergyInfoQueryDTO", description = "班组用能查询条件")
@Data
public class TeamGroupEnergyInfoQueryDTO {
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @ApiModelProperty(value = "节点id", required = true)
    private Long nodeId;

    @ApiModelProperty(value = "节点label", required = true)
    private String nodeLabel;

    @ApiModelProperty(value = "排班方案id", required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "能源类型", required = true)
    private Integer energyType;

    @ApiModelProperty(value = "周期", required = true)
    private Integer aggregationCycle;
}