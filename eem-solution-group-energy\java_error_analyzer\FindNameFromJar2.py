import os
import subprocess
import zipfile
import sys

def run_maven_build_classpath(project_path):
    """在多模块项目中构建classpath"""
    # 解析为绝对路径
    project_path = os.path.abspath(project_path)
    modules = ["eem-solution-group-energy-core"]
    all_jars = set()

    for module in modules:
        module_path = os.path.join(project_path, module)
        # 输出文件路径相对于模块目录
        classpath_file = "classpath.txt"
        
        # 自动检测mvn或mvn.cmd
        mvn_executable = "mvn.cmd" if sys.platform == "win32" else "mvn"
        cmd = [
            mvn_executable,
            "dependency:build-classpath",
            "-DincludeScope=compile",
            f"-Dmdep.outputFile={classpath_file}",
            "-Dmaven.main.skip=true",
            "-Dmaven.test.skip=true"
        ]
        
        print(f"在模块 {module} 中执行命令:", " ".join(cmd))
        proc = subprocess.run(cmd, cwd=module_path, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if proc.returncode != 0:
            print(f"模块 {module} 的Maven执行失败:")
            print(proc.stderr)
            continue
        
        # 构造classpath.txt的完整路径用于读取
        full_classpath_file = os.path.join(module_path, classpath_file)
        if not os.path.exists(full_classpath_file):
            print(f"模块 {module} 的classpath.txt文件未生成")
            continue
            
        with open(full_classpath_file, "r", encoding="utf-8") as f:
            jars = f.read().strip().split(os.pathsep)
            # 过滤掉空字符串
            all_jars.update(jar for jar in jars if jar)

    if not all_jars:
        print("所有模块都未能成功生成classpath。")
        return None

    print(f"共找到 {len(all_jars)} 个唯一的依赖jar")
    return list(all_jars)

def search_class_in_jars(class_name, jar_paths):
    """在JAR文件中搜索指定的类"""
    found = False
    suffix = f"/{class_name}.class"
    results = []
    
    for jar in jar_paths:
        try:
            with zipfile.ZipFile(jar, "r") as zipf:
                for entry in zipf.namelist():
                    if entry.endswith(suffix):
                        full_class_name = entry.replace("/", ".").replace(".class", "")
                        print(f"找到类 {class_name} 于 JAR: {jar}")
                        print(f"  全限定名: {full_class_name}")
                        results.append({
                            'class_name': class_name,
                            'full_qualified_name': full_class_name,
                            'jar_path': jar
                        })
                        found = True
        except Exception as e:
            # 忽略坏的zip文件，但记录错误
            print(f"警告: 无法读取JAR文件 {jar}: {e}")
            continue
    
    if not found:
        print(f"未找到类 {class_name}")
    
    return results


def main():
    """主函数"""
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 默认项目根目录是脚本所在目录的上一级
    default_project_path = os.path.join(script_dir, '..')

    if len(sys.argv) < 2:
        print("使用方法: python FindNameFromJar2.py <类名> [项目路径]")
        print(f"示例: python FindNameFromJar2.py UserService (将使用默认项目路径: {default_project_path})")
        sys.exit(1)
    
    class_name = sys.argv[1]
    # 如果提供了项目路径参数，则使用它，否则使用默认路径
    project_path = sys.argv[2] if len(sys.argv) > 2 else default_project_path
    
    # 确保项目路径是绝对路径
    project_path = os.path.abspath(project_path)
    
    print(f"搜索类: {class_name}")
    print(f"项目路径: {project_path}")
    print("-" * 50)
    
    # 运行Maven构建classpath
    jar_paths = run_maven_build_classpath(project_path)
    
    if jar_paths:
        # 搜索类
        results = search_class_in_jars(class_name, jar_paths)
        
        if results:
            print(f"\n搜索完成，找到 {len(results)} 个匹配项")
            for result in results:
                print(f"类名: {result['class_name']}")
                print(f"全限定名: {result['full_qualified_name']}")
                print(f"JAR路径: {result['jar_path']}")
                print("-" * 30)
        else:
            print(f"\n搜索完成，未找到类 {class_name}")
    else:
        print("无法获取项目依赖，请检查Maven配置")
        sys.exit(1)


if __name__ == "__main__":
    main()