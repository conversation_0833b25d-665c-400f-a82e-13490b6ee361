"""
上下文分析器

实现方法上下文分析和上下文整合功能
"""

import logging
import re
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass
from pathlib import Path

try:
    from .models import ErrorItem, MethodAnalysisResult
    from .legacy_code_searcher import OriginalMethod
except ImportError:
    from models import ErrorItem, MethodAnalysisResult
    from legacy_code_searcher import OriginalMethod


@dataclass
class MethodContext:
    """方法上下文信息"""
    class_info: Dict[str, Any]
    imports: List[str]
    package_declaration: str
    method_dependencies: List[str]
    field_dependencies: List[str]
    called_methods: List[str]
    business_context: str
    usage_scenarios: List[str]
    related_classes: List[str]


@dataclass
class IntegratedContext:
    """整合后的上下文信息"""
    business_description: str
    usage_scenarios: List[str]
    technical_context: str
    migration_notes: str
    recommendations: List[str]
    knowledge_base_matches: List[Dict[str, Any]]
    complexity_analysis: Dict[str, Any]


class MethodContextAnalyzer:
    """方法上下文分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_method_context(self, file_content: str, method_name: str, class_name: str) -> Optional[MethodContext]:
        """分析方法上下文"""
        try:
            # 基本的上下文分析
            return MethodContext(
                class_info={"name": class_name},
                imports=[],
                package_declaration="",
                method_dependencies=[],
                field_dependencies=[],
                called_methods=[],
                business_context=f"方法 {method_name} 位于类 {class_name}",
                usage_scenarios=["未知使用场景"],
                related_classes=[]
            )
        except Exception as e:
            self.logger.warning(f"分析方法上下文失败: {str(e)}")
            return None


class ContextIntegrator:
    """上下文整合器 - 整合当前项目错误上下文和迁移前方法实现"""
    
    def __init__(self, knowledge_base_path: str):
        self.knowledge_base_path = knowledge_base_path
        self.logger = logging.getLogger(__name__)
        self.method_context_analyzer = MethodContextAnalyzer()
        self.knowledge_base = {}
        self._load_knowledge_base()
    
    def _load_knowledge_base(self):
        """加载知识库"""
        try:
            knowledge_base_dir = Path(self.knowledge_base_path)
            if not knowledge_base_dir.exists():
                self.logger.warning(f"知识库路径不存在: {self.knowledge_base_path}")
                return
            
            for file_path in knowledge_base_dir.glob("*.md"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        self.knowledge_base[file_path.name] = content
                        self.logger.debug(f"加载知识库文件: {file_path.name}")
                except Exception as e:
                    self.logger.warning(f"加载知识库文件失败 {file_path}: {str(e)}")
            
            self.logger.info(f"知识库加载完成，共 {len(self.knowledge_base)} 个文件")
            
        except Exception as e:
            self.logger.error(f"加载知识库失败: {str(e)}")
    
    def integrate_context(self, error_item: ErrorItem, current_context: str, 
                         original_method: Optional[OriginalMethod] = None) -> IntegratedContext:
        """
        整合上下文信息
        
        Args:
            error_item: 错误项信息
            current_context: 当前项目中的错误上下文
            original_method: 迁移前的原始方法（可选）
            
        Returns:
            IntegratedContext: 整合后的上下文信息
        """
        try:
            self.logger.debug(f"开始整合上下文: {error_item.missing_method}")
            
            # 分析当前项目上下文
            current_method_context = self._analyze_current_context(current_context, error_item)
            
            # 分析原始方法上下文
            original_method_context = None
            if original_method:
                original_method_context = self._analyze_original_method_context(original_method)
            
            # 匹配知识库信息
            knowledge_matches = self._match_knowledge_base(error_item, original_method)
            
            # 生成业务描述
            business_description = self._generate_business_description(
                error_item, current_method_context, original_method_context, knowledge_matches
            )
            
            # 生成使用场景
            usage_scenarios = self._generate_usage_scenarios(
                error_item, current_method_context, original_method_context
            )
            
            # 生成技术上下文
            technical_context = self._generate_technical_context(
                current_method_context, original_method_context
            )
            
            # 生成迁移注意事项
            migration_notes = self._generate_migration_notes(
                error_item, original_method, knowledge_matches
            )
            
            # 生成建议
            recommendations = self._generate_recommendations(
                error_item, original_method, knowledge_matches
            )
            
            # 复杂度分析
            complexity_analysis = self._analyze_complexity(
                error_item, original_method, current_method_context
            )
            
            return IntegratedContext(
                business_description=business_description,
                usage_scenarios=usage_scenarios,
                technical_context=technical_context,
                migration_notes=migration_notes,
                recommendations=recommendations,
                knowledge_base_matches=knowledge_matches,
                complexity_analysis=complexity_analysis
            )
            
        except Exception as e:
            self.logger.error(f"整合上下文失败: {str(e)}")
            return IntegratedContext(
                business_description=f"方法 {error_item.missing_method} 的上下文分析",
                usage_scenarios=["未知使用场景"],
                technical_context="技术上下文分析失败",
                migration_notes="迁移注意事项分析失败",
                recommendations=["建议手动分析该方法"],
                knowledge_base_matches=[],
                complexity_analysis={"level": "unknown", "factors": []}
            )
    
    def _analyze_current_context(self, current_context: str, error_item: ErrorItem) -> Optional[MethodContext]:
        """分析当前项目上下文"""
        try:
            return self.method_context_analyzer.analyze_method_context(
                current_context, error_item.missing_method, error_item.class_name
            )
        except Exception as e:
            self.logger.warning(f"分析当前上下文失败: {str(e)}")
            return None
    
    def _analyze_original_method_context(self, original_method: OriginalMethod) -> Optional[MethodContext]:
        """分析原始方法上下文"""
        try:
            # 读取原始方法所在文件的完整内容
            with open(original_method.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                file_content = f.read()
            
            # 从method_code中提取方法名，确保method_code是字符串
            if isinstance(original_method.method_code, str):
                method_name = original_method.method_code.split('(')[0].split()[-1]
            else:
                method_name = "unknown"
            
            return self.method_context_analyzer.analyze_method_context(
                file_content, method_name, original_method.class_name
            )
            
        except Exception as e:
            self.logger.warning(f"分析原始方法上下文失败: {str(e)}")
            return None
    
    def _match_knowledge_base(self, error_item: ErrorItem, original_method: Optional[OriginalMethod] = None) -> List[Dict[str, Any]]:
        """匹配知识库信息"""
        try:
            matches = []
            
            # 搜索关键词
            search_terms = [
                error_item.missing_method,
                error_item.class_name,
                error_item.package.split('.')[-1] if error_item.package else "",
            ]
            
            if original_method:
                search_terms.extend([
                    original_method.class_name,
                    original_method.package_name.split('.')[-1] if original_method.package_name else ""
                ])
            
            # 在知识库中搜索匹配项
            for filename, content in self.knowledge_base.items():
                for term in search_terms:
                    if term and term.lower() in content.lower():
                        matches.append({
                            "file": filename,
                            "content": content[:500] + "..." if len(content) > 500 else content,
                            "relevance": "high" if term in error_item.missing_method else "medium"
                        })
                        break
            
            return matches
            
        except Exception as e:
            self.logger.warning(f"匹配知识库失败: {str(e)}")
            return []
    
    def _generate_business_description(self, error_item: ErrorItem, 
                                     current_context: Optional[MethodContext],
                                     original_context: Optional[MethodContext],
                                     knowledge_matches: List[Dict[str, Any]]) -> str:
        """生成业务描述"""
        try:
            description_parts = []
            
            if current_context:
                description_parts.append(current_context.business_context)
            
            # 从知识库获取业务信息
            if knowledge_matches:
                knowledge_info = " ".join([match["content"] for match in knowledge_matches[:2]])
                description_parts.append(f"知识库信息: {knowledge_info}")
            
            return ". ".join(description_parts) if description_parts else f"方法 {error_item.missing_method} 的业务描述"
            
        except Exception as e:
            self.logger.warning(f"生成业务描述失败: {str(e)}")
            return f"方法 {error_item.missing_method} 的业务描述"
    
    def _generate_usage_scenarios(self, error_item: ErrorItem,
                                current_context: Optional[MethodContext],
                                original_context: Optional[MethodContext]) -> List[str]:
        """生成使用场景"""
        try:
            scenarios = []
            
            if current_context and current_context.usage_scenarios:
                scenarios.extend(current_context.usage_scenarios)
            
            if original_context and original_context.usage_scenarios:
                scenarios.extend(original_context.usage_scenarios)
            
            if not scenarios:
                scenarios = [f"在 {error_item.class_name} 中调用 {error_item.missing_method}"]
            
            return list(set(scenarios))  # 去重
            
        except Exception as e:
            self.logger.warning(f"生成使用场景失败: {str(e)}")
            return ["未知使用场景"]
    
    def _generate_technical_context(self, current_context: Optional[MethodContext],
                                  original_context: Optional[MethodContext]) -> str:
        """生成技术上下文"""
        try:
            context_parts = []
            
            if current_context:
                if current_context.imports:
                    context_parts.append(f"导入: {', '.join(current_context.imports[:3])}")
                if current_context.method_dependencies:
                    context_parts.append(f"方法依赖: {', '.join(current_context.method_dependencies[:3])}")
            
            if original_context:
                if original_context.called_methods:
                    context_parts.append(f"调用方法: {', '.join(original_context.called_methods[:3])}")
            
            return "; ".join(context_parts) if context_parts else "技术上下文分析"
            
        except Exception as e:
            self.logger.warning(f"生成技术上下文失败: {str(e)}")
            return "技术上下文生成失败"
    
    def _generate_migration_notes(self, error_item: ErrorItem, 
                                original_method: Optional[OriginalMethod],
                                knowledge_matches: List[Dict[str, Any]]) -> str:
        """生成迁移注意事项"""
        try:
            notes = []
            
            # 从知识库获取迁移建议
            migration_suggestions = self._extract_migration_suggestions(knowledge_matches)
            if migration_suggestions:
                notes.extend(migration_suggestions)
            else:
                # 基本迁移注意事项
                notes.append(f"需要实现方法 {error_item.missing_method}")
                
                if error_item.in_param:
                    notes.append(f"注意参数类型匹配: {list(error_item.in_param.keys())}")
                
                if error_item.out_return:
                    notes.append(f"注意返回值类型: {error_item.out_return}")
                
                # 从原始方法分析迁移复杂度
                if original_method:
                    if original_method.dependencies:
                        notes.append(f"注意依赖项: {', '.join(original_method.dependencies[:3])}")
                    
                    if isinstance(original_method.method_code, str) and len(original_method.method_code) > 1000:
                        notes.append("方法较复杂，建议分步实现")
            
            return "; ".join(notes)
            
        except Exception as e:
            self.logger.warning(f"生成迁移注意事项失败: {str(e)}")
            return "迁移注意事项生成失败"
    
    def _extract_migration_suggestions(self, knowledge_matches: List[Dict[str, Any]]) -> List[str]:
        """从知识库中提取迁移建议"""
        try:
            suggestions = []
            
            for match in knowledge_matches:
                content = match.get("content", "")
                
                # 查找迁移相关的建议
                migration_patterns = [
                    r"迁移[建议|注意事项|要点][:：]([^。\n]+)",
                    r"注意[:：]([^。\n]+)",
                    r"建议[:：]([^。\n]+)",
                    r"需要[:：]([^。\n]+)"
                ]
                
                for pattern in migration_patterns:
                    matches_found = re.findall(pattern, content, re.IGNORECASE)
                    for suggestion in matches_found:
                        suggestions.append(suggestion.strip())
            
            return suggestions[:3]  # 最多返回3个建议
            
        except Exception as e:
            self.logger.warning(f"提取迁移建议失败: {str(e)}")
            return []
    
    def _generate_recommendations(self, error_item: ErrorItem,
                                original_method: Optional[OriginalMethod],
                                knowledge_matches: List[Dict[str, Any]]) -> List[str]:
        """生成建议"""
        try:
            recommendations = []
            
            # 基本建议
            recommendations.append(f"参考原始方法实现 {error_item.missing_method}")
            
            if original_method:
                recommendations.append(f"查看原始文件: {original_method.file_path}")
                
                if original_method.javadoc:
                    recommendations.append("参考原始方法的Javadoc文档")
                
                if original_method.annotations:
                    recommendations.append(f"注意原始注解: {', '.join(original_method.annotations[:2])}")
            
            # 从知识库获取建议
            if knowledge_matches:
                recommendations.append("参考知识库中的相关信息")
            
            return recommendations
            
        except Exception as e:
            self.logger.warning(f"生成建议失败: {str(e)}")
            return ["建议手动分析该方法"]
    
    def _analyze_complexity(self, error_item: ErrorItem,
                          original_method: Optional[OriginalMethod],
                          current_context: Optional[MethodContext]) -> Dict[str, Any]:
        """分析复杂度"""
        try:
            complexity_factors = []
            complexity_level = "low"
            
            if original_method:
                # 基于方法代码长度判断复杂度
                if isinstance(original_method.method_code, str):
                    code_length = len(original_method.method_code)
                    if code_length > 2000:
                        complexity_level = "high"
                        complexity_factors.append("方法代码较长")
                    elif code_length > 1000:
                        complexity_level = "medium"
                        complexity_factors.append("方法代码中等长度")
                
                # 基于依赖数量判断复杂度
                if original_method.dependencies and len(original_method.dependencies) > 5:
                    complexity_level = "high" if complexity_level != "high" else complexity_level
                    complexity_factors.append("依赖项较多")
            
            return {
                "level": complexity_level,
                "factors": complexity_factors
            }
            
        except Exception as e:
            self.logger.warning(f"分析复杂度失败: {str(e)}")
            return {"level": "unknown", "factors": []}