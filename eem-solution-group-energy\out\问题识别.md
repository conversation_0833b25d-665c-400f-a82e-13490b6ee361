# 代码扫描问题报告

总问题数: 147

## ClassesConfigDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfig"
calling_class: "ClassesConfigDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;"
line: ["[4, 11]"]

## ClassesConfigDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfigDao"
calling_class: "ClassesConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfigDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.ClassesConfigDao;"
line: ["[4, 14]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfig"
calling_class: "ClassesConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;"
line: ["[5, 14]"]

## ClassesConfigVO

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfig"
calling_class: "ClassesConfigVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;"
line: ["[3, 36]"]

## ClassesSchemeDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesScheme"
calling_class: "ClassesSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;"
line: ["[4, 18, 11]"]

## ClassesSchemeDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeDao"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesSchemeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.ClassesSchemeDao;"
line: ["[7, 21]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesScheme"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;"
line: ["[8, 21, 30, 39, 39]"]

## ClassesSchemeVO

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesScheme"
calling_class: "ClassesSchemeVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;"
line: ["[3, 34]"]

## HolidayConfigDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "HolidayConfig"
calling_class: "HolidayConfigDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'HolidayConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig;"
line: ["[4, 21, 13]"]

## HolidayConfigDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "HolidayConfigDao"
calling_class: "HolidayConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'HolidayConfigDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.HolidayConfigDao;"
line: ["[5, 19]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "HolidayConfig"
calling_class: "HolidayConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'HolidayConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig;"
line: ["[6, 28, 34, 34, 19]"]

## SchedulingClassesDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClasses"
calling_class: "SchedulingClassesDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingClasses' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingClasses;"
line: ["[4, 48, 39, 30, 13, 21]"]

## SchedulingClassesDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesDao"
calling_class: "SchedulingClassesDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingClassesDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao;"
line: ["[5, 20]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClasses"
calling_class: "SchedulingClassesDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingClasses' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingClasses;"
line: ["[6, 65, 70, 70, 30, 34, 34, 85, 90, 90, 48, 52, 52, 20]"]

## SchedulingScheme

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "SchedulingScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeAddUpdateDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeAddUpdateDTO;"
line: ["[6, 45, 45]"]

## SchedulingSchemeDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingScheme"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;"
line: ["[5, 15, 39, 46, 31, 24]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO;"
line: ["[6, 24]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "ResultWithTotal"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[24]"]

### 问题 4
error_type: "类问题"
error_code: "method_return_type"
calling_class: "SchedulingSchemeDao"
calling_method: "pageQuery"
return_type: "ResultWithTotal<List<SchedulingScheme>>"
line: ["24"]

## SchedulingSchemeDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeDao"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao;"
line: ["[9, 26]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingScheme"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;"
line: ["[10, 98, 102, 67, 70, 26, 45, 55, 55, 57, 80, 85, 85]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO;"
line: ["[11, 45]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "ResultWithTotal"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[45]"]

### 问题 5
error_type: "类问题"
error_code: "method_return_type"
calling_class: "SchedulingSchemeDaoImpl"
calling_method: "pageQuery"
return_type: "ResultWithTotal<List<SchedulingScheme>>"
line: ["45"]

## SchedulingSchemeToNodeDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeToNode"
calling_class: "SchedulingSchemeToNodeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNode' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode;"
line: ["[4, 21, 13]"]

## SchedulingSchemeToNodeDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeToNodeDao"
calling_class: "SchedulingSchemeToNodeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNodeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao;"
line: ["[5, 17, 17]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeToNode"
calling_class: "SchedulingSchemeToNodeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNode' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode;"
line: ["[6, 17, 17, 26, 27, 27]"]

## SchedulingSchemeVO

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingScheme"
calling_class: "SchedulingSchemeVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;"
line: ["[3, 32]"]

## TeamConfigController

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[171, 160]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoAddUpdateDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[131]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeDetailVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[62, 54, 48]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[145]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeAddUpdateDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[108]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[114]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesSaveDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[154]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[42]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "ResultWithTotal"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[48]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[48]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeRelatedNodeDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[93]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeRelatedHolidayDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[78]"]

### 问题 13
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["35"]

### 问题 14
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateSchedulingScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["40"]

### 问题 15
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingScheme"
return_type: "ResultWithTotal<List<SchedulingSchemeDetailVO>>"
line: ["48"]

### 问题 16
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "deleteSchedulingScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["68"]

### 问题 17
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingSchemeRelatedHoliday"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["76"]

### 问题 18
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingSchemeRelatedNode"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["91"]

### 问题 19
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateClassesScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["106"]

### 问题 20
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "deleteClassesScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["121"]

### 问题 21
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateTeamGroupInfo"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["129"]

### 问题 22
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "deleteTeamGroupInfo"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["137"]

### 问题 23
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingClasses"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["152"]

## TeamConfigService

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ResultWithTotal"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[31, 31]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeDetailVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[31, 31, 38, 161]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[31, 31]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[23, 23]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[144, 144, 154]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeAddUpdateDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[87, 87]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeRelatedNodeDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[71, 71]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeRelatedHolidayDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[55, 55]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[127]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoAddUpdateDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[111]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesSaveDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[135]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[95]"]

### 问题 13
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigService"
calling_method: "querySchedulingScheme"
return_type: "ResultWithTotal<List<SchedulingSchemeDetailVO>>"
line: ["31"]

## TeamConfigServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "EemCloudAuthService"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'EemCloudAuthService' 已废弃，请寻找替代方案或移除相关代码"
line: ["[11]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamConfigService"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamConfigService' 使用: import com.cet.eem.fusion.groupenergy.core.service.TeamConfigService;"
line: ["[12, 30]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeRelatedNodeDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[237]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeToNode"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[239, 250, 252, 252, 273, 164]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesScheme"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[416, 401, 311, 330, 330, 340, 348, 773, 147]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClasses"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[427, 569, 575, 577, 577, 675, 690, 697, 702, 500, 463, 358, 604, 615, 622, 627, 138, 156]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[64]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingScheme"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[66, 79, 79, 746, 110, 516, 392, 93, 442, 305, 761, 763, 126]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesSaveDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[567]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesConfigDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[576]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeDetailVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[745, 109, 91, 94, 95, 761, 762, 764, 764]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[668, 696, 698, 698, 598, 621, 623, 623]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfo"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[693, 705, 521, 447, 454, 454, 471, 479, 479, 618, 630, 782]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfig"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[694, 715, 319, 321, 321, 354, 366, 368, 368, 619, 640]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingClassesConfigVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[701, 703, 703, 626, 628, 628]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeRelatedHolidayDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[191]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "HolidayConfig"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[194, 203, 205, 205, 222]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[514, 515, 522, 522, 781, 783, 783]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "Result"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[531]"]

### 问题 20
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[390, 400, 402, 402, 772, 774, 774]"]

### 问题 21
error_type: "类问题"
error_code: "类问题"
missing_class: "ResultWithTotal"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[91, 93, 95, 95]"]

### 问题 22
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[91]"]

### 问题 23
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoAddUpdateDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[438]"]

### 问题 24
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesSchemeAddUpdateDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[289]"]

### 问题 25
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfigDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[290, 292, 293, 320, 367]"]

### 问题 26
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["32"]

### 问题 27
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["35"]

### 问题 28
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["38"]

### 问题 29
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["41"]

### 问题 30
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["44"]

### 问题 31
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["47"]

### 问题 32
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["50"]

### 问题 33
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["53"]

### 问题 34
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigServiceImpl"
calling_method: "querySchedulingScheme"
return_type: "ResultWithTotal<List<SchedulingSchemeDetailVO>>"
line: ["91"]

### 问题 35
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamConfigServiceImpl"
calling_method: "TeamConfigServiceImpl -> EemCloudAuthService"
suggest: "EemCloudAuthService废弃"
line: ["{'声明': 54, '使用': [531]}"]

### 问题 36
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamConfigServiceImpl"
calling_method: "TeamConfigServiceImpl -> NodeDao"
suggest: "NodeDao已经废弃，考虑通过EemNodeService重构"
line: ["{'声明': 39, '使用': [164, 167, 239, 245, 260, 273]}"]

## TeamEnergyController

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesEnergyInfoQueryDTO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;"
line: ["[5, 55]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyInfoQueryDTO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;"
line: ["[6, 37, 43, 49]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesEnergyInfoVO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;"
line: ["[7, 49, 55]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyHistogramVO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyHistogramVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;"
line: ["[8, 43]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyInfoVO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;"
line: ["[9, 37]"]

### 问题 6
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["32"]

## TeamEnergyService

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesEnergyInfoQueryDTO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;"
line: ["[3, 49]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyInfoQueryDTO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;"
line: ["[4, 33, 41, 25]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesEnergyInfoVO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;"
line: ["[5, 41, 49]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyHistogramVO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyHistogramVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;"
line: ["[6, 33]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyInfoVO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;"
line: ["[7, 25]"]

## TeamEnergyServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeDao"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao;"
line: ["[13]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeToNodeDao"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNodeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao;"
line: ["[14]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyDao"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao;"
line: ["[15]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesEnergyInfoQueryDTO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;"
line: ["[17, 372]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyInfoQueryDTO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;"
line: ["[18, 153, 65, 271]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesEnergyInfoVO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;"
line: ["[19, 372, 373, 373, 436, 446, 446, 271, 272, 310, 310, 319, 319]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyCard"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyCard' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard;"
line: ["[20, 101, 103, 103]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyHistogramVO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyHistogramVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;"
line: ["[21, 153, 190, 193, 193, 204, 217, 217, 232, 232]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyInfoVO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;"
line: ["[22, 65, 67, 67]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "UnitService"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'UnitService' 已废弃，请寻找替代方案或移除相关代码"
line: ["[23]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "NodeServiceImpl"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'NodeServiceImpl' 使用: import com.cet.electric.baseconfig.sdk.service.impl.NodeServiceImpl;"
line: ["[24]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamEnergyService"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamEnergyService' 使用: import com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService;"
line: ["[25, 41]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingScheme"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[376, 161, 70, 275]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesConfig"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[382, 441, 170, 219, 281, 320]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesScheme"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[383, 437, 282]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergy"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[396, 407, 178, 199, 204, 205, 208, 216, 217, 217, 232, 232, 80, 98, 102, 106, 294, 305, 316]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfo"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[427, 167, 211, 76, 108]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "ClassesName"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ClassesName' 已废弃，请寻找替代方案或移除相关代码"
line: ["[436, 446, 446, 319, 319]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "SchedulingSchemeToNode"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[468]"]

### 问题 20
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["43"]

### 问题 21
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["46"]

### 问题 22
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["49"]

### 问题 23
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["52"]

### 问题 24
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["55"]

### 问题 25
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> NodeService"
suggest: "请使用EemNodeService替代NodeService"
line: ["{'声明': 53, '使用': [466]}"]

### 问题 26
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> NodeServiceImpl"
suggest: "请使用EemNodeService替代NodeServiceImpl"
line: ["{'声明': 53, '使用': [466]}"]

### 问题 27
error_type: "单位服务变更详细信息"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> UnitService"
suggest: "UnitService已经废弃，考虑通过EnergyUnitService重构"
line: ["{'声明': 50, '使用': [88, 186, 302, 404]}"]

### 问题 28
error_type: "单位服务变更详细信息"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> UnitService"
suggest: "UnitService已经废弃，考虑通过EnergyUnitService重构"
line: ["{'声明': 50, '使用': [88, 186, 302, 404]}"]

### 问题 29
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> NodeDao"
suggest: "NodeDao已经废弃，考虑通过EemNodeService重构"
line: ["{'声明': 56, '使用': [468]}"]

## TeamGroupEnergyDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergy"
calling_class: "TeamGroupEnergyDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergy' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;"
line: ["[4, 40, 54, 27, 13]"]

## TeamGroupEnergyDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergyDao"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao;"
line: ["[6, 20]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupEnergy"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergy' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;"
line: ["[7, 20, 34, 39, 39, 94, 99, 99, 64, 68, 68]"]

## TeamGroupInfoDao

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfo"
calling_class: "TeamGroupInfoDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfo' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;"
line: ["[4, 11]"]

## TeamGroupInfoDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfoDao"
calling_class: "TeamGroupInfoDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfoDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupInfoDao;"
line: ["[4, 14]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfo"
calling_class: "TeamGroupInfoDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfo' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;"
line: ["[5, 14]"]

## TeamGroupInfoVO

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TeamGroupInfo"
calling_class: "TeamGroupInfoVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfo' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;"
line: ["[3, 39]"]
