package com.cet.eem.fusion.groupenergy.core.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 班次用能信息VO
 *
 * <AUTHOR>
 */
@ApiModel(value = "ClassesEnergyInfoVO", description = "班次用能信息VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassesEnergyInfoVO {

    @ApiModelProperty(value = "班次id")
    private List<Long> classesConfigIdList;

    @ApiModelProperty(value = "班次,班次方案名称")
    private List<ClassesName> classesNames;

    @ApiModelProperty(value = "班组名称")
    private String teamGroupNames;

    @ApiModelProperty(value = "班组id集合")
    private List<Long> teamGroupIdList;

    @ApiModelProperty(value = "用能占比")
    private Double energyProportion;

    @ApiModelProperty(value = "用能量")
    private Double energyTotal;

    @ApiModelProperty(value = "平均班次能耗")
    private Double avgEnergy;

    @ApiModelProperty(value = "能耗单位")
    private String energyUnit;

    @Getter
    @Setter
    public static class ClassesName {

        @ApiModelProperty(value = "班次名称")
        private String configName;

        @ApiModelProperty(value = "班次方案名称")
        private String schemeName;
    }

}
