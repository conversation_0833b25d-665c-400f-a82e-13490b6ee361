# 源码上下文提取器配置文件

# 路径配置
paths:
  # 当前项目源码路径（当前工作目录）
  src_path: "."
  
  # 迁移前源码路径
  legacy_src_path: "E:/work/project/cet-piem-plat-1.3/cet-piem-component/piem-business-modules/piem-business-teamenergy-starter"
  
  # 知识库路径
  knowledge_base_path: "知识库"

# 处理配置
processing:
  # 启用AST解析
  enable_ast_parsing: true
  
  # 启用文本解析回退
  enable_text_fallback: false
  
  # 最大上下文行数
  max_context_lines: 200
  
  # 处理超时时间（秒）
  timeout_seconds: 30

# 输出配置
output:
  # 输出文件路径
  output_path: "out/source_context_analysis.md"

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: "DEBUG"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 控制台输出
  console: true
  
  # 文件输出
  file: true
  
  # 日志文件路径
  log_file: "logs/source_context_extractor.log"