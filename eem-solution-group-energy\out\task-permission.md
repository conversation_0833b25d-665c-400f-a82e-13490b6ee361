# 权限 ID 调整问题解决方案

## 问题统计
- 总问题数: 9 个
- 涉及文件: 1 个 (TeamConfigController.java)
- 需要修改的常量: 4 个 (SCHEDULING_SCHEME, CLASSES_SCHEME, TEAM_GROUP_INFO, SCHEDULING_CLASSES)

## TeamConfigController.java

### 权限ID问题 1: addOrUpdateSchedulingScheme方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 40
- **问题方法**: addOrUpdateSchedulingScheme
- **当前权限ID**: 122 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.SCHEDULING_SCHEME 常量值为 10122
- **修复操作**: 
  1. 修改常量类: public static final int SCHEDULING_SCHEME = 10122;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 2: deleteSchedulingScheme方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 68
- **问题方法**: deleteSchedulingScheme
- **当前权限ID**: 122 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.SCHEDULING_SCHEME 常量值为 10122
- **修复操作**: 
  1. 修改常量类: public static final int SCHEDULING_SCHEME = 10122;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 3: saveSchedulingSchemeRelatedHoliday方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 76
- **问题方法**: saveSchedulingSchemeRelatedHoliday
- **当前权限ID**: 122 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.SCHEDULING_SCHEME 常量值为 10122
- **修复操作**: 
  1. 修改常量类: public static final int SCHEDULING_SCHEME = 10122;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 4: saveSchedulingSchemeRelatedNode方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 91
- **问题方法**: saveSchedulingSchemeRelatedNode
- **当前权限ID**: 122 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.SCHEDULING_SCHEME 常量值为 10122
- **修复操作**: 
  1. 修改常量类: public static final int SCHEDULING_SCHEME = 10122;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 5: addOrUpdateClassesScheme方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 106
- **问题方法**: addOrUpdateClassesScheme
- **当前权限ID**: 123 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.CLASSES_SCHEME 常量值为 10123
- **修复操作**: 
  1. 修改常量类: public static final int CLASSES_SCHEME = 10123;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 6: deleteClassesScheme方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 121
- **问题方法**: deleteClassesScheme
- **当前权限ID**: 123 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.CLASSES_SCHEME 常量值为 10123
- **修复操作**: 
  1. 修改常量类: public static final int CLASSES_SCHEME = 10123;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 7: addOrUpdateTeamGroupInfo方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 129
- **问题方法**: addOrUpdateTeamGroupInfo
- **当前权限ID**: 124 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.TEAM_GROUP_INFO 常量值为 10124
- **修复操作**: 
  1. 修改常量类: public static final int TEAM_GROUP_INFO = 10124;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 8: deleteTeamGroupInfo方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 137
- **问题方法**: deleteTeamGroupInfo
- **当前权限ID**: 124 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.TEAM_GROUP_INFO 常量值为 10124
- **修复操作**: 
  1. 修改常量类: public static final int TEAM_GROUP_INFO = 10124;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

### 权限ID问题 9: saveSchedulingClasses方法权限ID超范围 (🟢 绿色标记)
- **问题位置**: 行号 152
- **问题方法**: saveSchedulingClasses
- **当前权限ID**: 125 (超出范围)
- **解决方案**: 修改 GroupEnergyConstantDef.SCHEDULING_CLASSES 常量值为 10125
- **修复操作**: 
  1. 修改常量类: public static final int SCHEDULING_CLASSES = 10125;
  2. 注解中的引用无需修改，会自动使用新的常量值
- **分类依据**: 知识库第4条权限ID调整规则，范围[10000-20000]

## 修复汇总

### 需要修改的常量类文件
- **文件路径**: eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/def/GroupEnergyConstantDef.java

### 具体修改内容
```java
// 原代码
public static final int SCHEDULING_SCHEME = 122;
public static final int CLASSES_SCHEME = 123;
public static final int TEAM_GROUP_INFO = 124;
public static final int SCHEDULING_CLASSES = 125;

// 修改后代码 (原ID + 10000)
public static final int SCHEDULING_SCHEME = 10122;
public static final int CLASSES_SCHEME = 10123;
public static final int TEAM_GROUP_INFO = 10124;
public static final int SCHEDULING_CLASSES = 10125;
```

### 分类统计
- 🟢 绿色标记 (确定性修复): 9 个
- 🟡 黄色标记 (需要AI判断): 0 个
- 🔴 红色标记 (未识别): 0 个

## 验证完整性
- ✅ 问题数量核对: 9 个权限ID调整问题全部处理
- ✅ 文件覆盖验证: TeamConfigController.java 的所有权限ID问题已处理
- ✅ 解决方案完整性: 每个问题都有具体的修复操作和分类依据
- ✅ 知识库匹配: 所有解决方案都基于知识库第4条权限ID调整规则
