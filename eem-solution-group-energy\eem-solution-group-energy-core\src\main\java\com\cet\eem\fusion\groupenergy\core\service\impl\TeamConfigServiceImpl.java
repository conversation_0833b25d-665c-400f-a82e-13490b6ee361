package com.cet.eem.fusion.groupenergy.core.service.impl;

import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.exception.BusinessBaseException;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.config.sdk.auth.service.NodeAuthCheckService;
import com.cet.eem.fusion.groupenergy.core.service.TeamConfigService;
import com.cet.eem.fusion.groupenergy.core.dao.*;
import com.cet.eem.fusion.groupenergy.core.entity.po.*;
import com.cet.eem.fusion.groupenergy.core.entity.dto.*;
import com.cet.eem.fusion.groupenergy.core.entity.vo.*;
import com.cet.eem.fusion.common.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 班组配置业务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TeamConfigServiceImpl implements TeamConfigService {

    @Autowired
    private SchedulingSchemeDao schedulingSchemeDao;

    @Autowired
    private HolidayConfigDao holidayConfigDao;

    @Autowired
    private SchedulingSchemeToNodeDao schedulingSchemeToNodeDao;

    @Autowired
    private ClassesSchemeDao classesSchemeDao;

    @Autowired
    private ClassesConfigDao classesConfigDao;

    @Autowired
    private SchedulingClassesDao schedulingClassesDao;

    @Autowired
    private TeamGroupInfoDao teamGroupInfoDao;

    @Autowired
    private NodeAuthCheckService nodeAuthCheckService;


    /**
     * 新增修改排班方案
     *
     * @param dto 排班方案
     * @return Boolean
     */
    @Override
    public Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto) {
        //新增修改时候，先查询所有的排班方案，看名称是否存在
        List<SchedulingScheme> schedulingSchemes = schedulingSchemeDao.selectAll();
        List<String> schemeName;
        if (Objects.nonNull(dto.getId())) {
            schemeName = schedulingSchemes.stream().filter(s -> !Objects.equals(dto.getId(), s.getId()))
                    .map(SchedulingScheme::getName).collect(Collectors.toList());
        } else {
            schemeName = schedulingSchemes.stream().map(SchedulingScheme::getName).collect(Collectors.toList());
        }

        if (schemeName.contains(dto.getName())) {
            throw new BusinessBaseException("排班方案名称已存在，请修改！");
        }

        SchedulingScheme schedulingScheme = new SchedulingScheme(dto);
        schedulingSchemeDao.saveOrUpdateBatch(Collections.singletonList(schedulingScheme));
        return Boolean.TRUE;
    }

    /**
     * 查询排班方案
     *
     * @param dto 查询条件
     * @return 排班方案
     */
    @Override
    public ApiResult<List<SchedulingSchemeDetailVO>> querySchedulingScheme(SchedulingSchemeQueryDTO dto) {
        //根据条件分页查询排班方案
        ApiResult<List<SchedulingScheme>> resultWithTotal = schedulingSchemeDao.pageQuery(dto);
        List<SchedulingSchemeDetailVO> schedulingSchemeDetailVOS = getSchedulingSchemeDetailVOS(resultWithTotal.getData());
        ApiResult<List<SchedulingSchemeDetailVO>> result = new ApiResult<>();
        result.setData(schedulingSchemeDetailVOS);
        result.setTotal(resultWithTotal.getTotal());
        result.setCode(resultWithTotal.getCode());
        result.setMsg(resultWithTotal.getMsg());
        return result;
    }

    /**
     * 查询所有排班方案以及关联班组班次
     *
     * @return 所有排班方案以及关联班组班次
     */
    @Override
    public List<SchedulingSchemeDetailVO> allSchedulingScheme() {
        List<SchedulingScheme> schedulingSchemes = schedulingSchemeDao.queryAll();
        if (CollectionUtils.isEmpty(schedulingSchemes)) {
            return Collections.emptyList();
        }
        return getSchedulingSchemeDetailVOS(schedulingSchemes);
    }

    /**
     * 删除方案
     *
     * @param id 方案id
     * @return Boolean
     */
    @Override
    public void deleteSchedulingScheme(Long id) {
        //如果班次和班组已经排班则不能删除！
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(id);
        if (Objects.isNull(schedulingScheme)) {
            return;
        }

        //查询排班方案下的班次和班组，
        List<Long> teamGroupIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(schedulingScheme.getTeamgroupinfo_model())) {
            teamGroupIds = schedulingScheme.getTeamgroupinfo_model().stream()
                    .map(TeamGroupInfo::getId)
                    .collect(Collectors.toList());

            List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByTeamGroup(teamGroupIds);
            if (CollectionUtils.isNotEmpty(schedulingClassesList)) {
                throw new BusinessBaseException("当前排班方案已进行排班，不可删除！");
            }
        }

        List<Long> classesConfigIds = new ArrayList<>();
        List<Long> classesSchemeIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(schedulingScheme.getClassesscheme_model())) {
            for (ClassesScheme classesScheme : schedulingScheme.getClassesscheme_model()) {
                classesSchemeIds.add(classesScheme.getId());
                if (CollectionUtils.isEmpty(classesScheme.getClassesconfig_model())) {
                    continue;
                }
                classesScheme.getClassesconfig_model().forEach(c -> classesConfigIds.add(c.getId()));
            }

            if (CollectionUtils.isNotEmpty(classesConfigIds)) {
                List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByClassesConfig(classesConfigIds);
                if (CollectionUtils.isNotEmpty(schedulingClassesList)) {
                    throw new BusinessBaseException("当前排班方案已经排班，不可删除！");
                }
            }
        }

        //查询排班方案关联节点，然后删除
        List<SchedulingSchemeToNode> schemeToNodes = schedulingSchemeToNodeDao.queryBySchedulingSchemeId(id);
        if (CollectionUtils.isNotEmpty(schemeToNodes)) {
            List<Long> toNodeIds = schemeToNodes.stream().map(SchedulingSchemeToNode::getId).collect(Collectors.toList());
            schedulingSchemeToNodeDao.deleteBatchIds(toNodeIds);
        }

        //先删除班次配置，在删除班次方案  再删除班组方案 最后删除排班方案
        if (CollectionUtils.isNotEmpty(classesConfigIds)) {
            classesConfigDao.deleteBatchIds(classesConfigIds);
        }
        if (CollectionUtils.isNotEmpty(classesSchemeIds)) {
            classesSchemeDao.deleteBatchIds(classesSchemeIds);
        }
        if (CollectionUtils.isNotEmpty(teamGroupIds)) {
            teamGroupInfoDao.deleteBatchIds(teamGroupIds);
        }
        schedulingSchemeDao.deleteById(id);
    }


    /**
     * 排班方案关联节假日
     *
     * @param dto 排班方案假期关联关系
     * @return Boolean
     */
    @Override
    public Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto) {

        //删除方案对应的所有节假日
        List<HolidayConfig> configs = holidayConfigDao.queryBySchedulingScheme(dto.getSchedulingSchemeId());
        if (CollectionUtils.isNotEmpty(configs)) {
            List<Long> configIds = configs.stream().map(HolidayConfig::getId).collect(Collectors.toList());
            holidayConfigDao.deleteBatchIds(configIds);
        }

        //保存最新的方案节假日
        List<Long> holidayList = dto.getHolidayList();

        List<HolidayConfig> holidayConfigs = new ArrayList<>();
        for (Long holiday : holidayList) {
            HolidayConfig config = new HolidayConfig();
            config.setSchedulingSchemeId(dto.getSchedulingSchemeId());
            config.setHolidayDate(holiday);
            holidayConfigs.add(config);
        }
        holidayConfigDao.saveOrUpdateBatch(holidayConfigs);
        return Boolean.TRUE;
    }

    /**
     * 查询排班方案关联节假日
     *
     * @param schedulingSchemeId 排班方案id
     * @return 关联节假日
     */
    @Override
    public List<Long> querySchedulingSchemeRelatedHoliday(Long schedulingSchemeId) {
        List<HolidayConfig> configs = holidayConfigDao.queryBySchedulingScheme(schedulingSchemeId);

        if (CollectionUtils.isNotEmpty(configs)) {
            return configs.stream().map(HolidayConfig::getHolidayDate).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 保存排班方案关联节点
     *
     * @param dto 方案关联节点
     * @return Boolean
     */
    @Override
    public Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto) {
        //删除排班方案关联节点
        List<SchedulingSchemeToNode> deleteToNodes = schedulingSchemeToNodeDao.queryBySchedulingSchemeId(dto.getSchedulingSchemeId());

        if (CollectionUtils.isNotEmpty(deleteToNodes)) {
            List<Long> toNodeIdList = deleteToNodes.stream()
                    .map(SchedulingSchemeToNode::getId)
                    .collect(Collectors.toList());
            schedulingSchemeToNodeDao.deleteBatchIds(toNodeIdList);
        }

        //保存排班方案最新关联节点
        List<BaseVo> nodeList = dto.getNodeList();
        List<SchedulingSchemeToNode> saveToNodeList = new ArrayList<>();
        for (BaseVo baseVo : nodeList) {
            SchedulingSchemeToNode toNode = new SchedulingSchemeToNode();

            toNode.setSchedulingSchemeId(dto.getSchedulingSchemeId());
            toNode.setObjectId(baseVo.getId());
            toNode.setObjectLabel(baseVo.getModelLabel());

            saveToNodeList.add(toNode);
        }
        schedulingSchemeToNodeDao.saveOrUpdateBatch(saveToNodeList);
        return Boolean.TRUE;
    }


    /**
     * 查询排班方案关联节点
     *
     * @param schedulingSchemeId 排版方案id
     * @return 排班方案关联节点
     */
    @Override
    public List<BaseVo> querySchedulingSchemeRelatedNode(Long schedulingSchemeId) {
        List<SchedulingSchemeToNode> schemeToNodes = schedulingSchemeToNodeDao.queryBySchedulingSchemeId(schedulingSchemeId);

        if (CollectionUtils.isNotEmpty(schemeToNodes)) {
            return schemeToNodes.stream()
                    .map(s -> new BaseVo(s.getObjectId(), s.getObjectLabel())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 新增修改班次方案
     *
     * @param dto 班次方案
     * @return Boolean
     */
    @Override
    public Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto) {
        List<ClassesConfigDTO> classesConfigVOList = dto.getClassesConfigVOList();

        ClassesConfigDTO classesConfigDTO1 = classesConfigVOList.get(0);
        ClassesConfigDTO classesConfigDTO2 = classesConfigVOList.get(classesConfigVOList.size() - 1);

        Long hour = classesConfigDTO2.getEndTime() - classesConfigDTO1.getStartTime();
        if (hour > 24 * TimeUtil.HOUR) {
            throw new BusinessBaseException("班次总时长不能大于24小时，请检查");
        }

        long distinctCount = dto.getClassesConfigVOList().stream().map(ClassesConfigDTO::getName).distinct().count();
        if (distinctCount != dto.getClassesConfigVOList().size()) {
            throw new BusinessBaseException("同一班次方案下，班次名称不允许重复，请修改！");
        }

        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());

        if (Objects.isNull(dto.getId())) {
            //新增
            //验证班次方案名称是否已存在
            if (CollectionUtils.isNotEmpty(schedulingScheme.getClassesscheme_model())) {
                Optional<ClassesScheme> first = schedulingScheme.getClassesscheme_model().stream()
                        .filter(s -> Objects.equals(s.getName(), dto.getName()))
                        .findFirst();
                if (first.isPresent()) {
                    throw new BusinessBaseException("当前排班方案下，班次方案名称已存在，请修改！");
                }
            }

            List<ClassesConfig> classesConfigs = new ArrayList<>();
            for (ClassesConfigDTO classesConfigDTO : dto.getClassesConfigVOList()) {
                ClassesConfig config = new ClassesConfig();
                classesConfigs.add(config);

                config.setName(classesConfigDTO.getName());
                config.setStartTime(classesConfigDTO.getStartTime());
                config.setEndTime(classesConfigDTO.getEndTime());
                config.setSerialNumber(classesConfigDTO.getOrder());
            }

            ClassesScheme classesScheme = new ClassesScheme();
            classesScheme.setName(dto.getName());
            classesScheme.setClassesconfig_model(classesConfigs);

            schedulingScheme.setClassesscheme_model(Collections.singletonList(classesScheme));
            schedulingSchemeDao.updateById(schedulingScheme);
        } else {
            //更新
            //验证班次方案名称是否已存在
            if (CollectionUtils.isNotEmpty(schedulingScheme.getClassesscheme_model())) {
                Optional<ClassesScheme> first = schedulingScheme.getClassesscheme_model().stream()
                        .filter(s -> !Objects.equals(s.getId(), dto.getId()) && Objects.equals(s.getName(), dto.getName()))
                        .findFirst();
                if (first.isPresent()) {
                    throw new BusinessBaseException("当前排班方案下，班次方案名称已存在，请修改！");
                }
            }

            ClassesScheme classesScheme = classesSchemeDao.queryById(dto.getId());
            if (Objects.isNull(classesScheme)) {
                return Boolean.TRUE;
            }

            //删除旧的班次配置，若班次已进行排班则不可修改
            List<ClassesConfig> deleteClassesConfigList = classesScheme.getClassesconfig_model();
            if (CollectionUtils.isNotEmpty(deleteClassesConfigList)) {
                List<Long> deleteClassesConfigIds = deleteClassesConfigList.stream().map(ClassesConfig::getId).collect(Collectors.toList());

                List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByClassesConfig(deleteClassesConfigIds);
                if (CollectionUtils.isNotEmpty(schedulingClassesList)) {
                    throw new BusinessBaseException("当前排班方案下班次已进行排班，不可修改");
                }
                classesConfigDao.deleteBatchIds(deleteClassesConfigIds);
            }


            List<ClassesConfig> classesConfigs = new ArrayList<>();
            for (ClassesConfigDTO classesConfigDTO : dto.getClassesConfigVOList()) {
                ClassesConfig config = new ClassesConfig();
                classesConfigs.add(config);

                config.setName(classesConfigDTO.getName());
                config.setStartTime(classesConfigDTO.getStartTime());
                config.setEndTime(classesConfigDTO.getEndTime());
                config.setSerialNumber(classesConfigDTO.getOrder());
            }
            classesScheme.setName(dto.getName());
            classesScheme.setClassesconfig_model(classesConfigs);
            classesSchemeDao.updateById(classesScheme);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询班次方案
     *
     * @param id 排班方案id
     * @return 班次方案
     */
    @Override
    public List<ClassesSchemeVO> queryClassesScheme(Long id) {
        // 查询排班方案以及关联班次方案和班次
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(id);

        // 无数据则返回空
        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getClassesscheme_model())) {
            return Collections.emptyList();
        }

        // 封装展示数据返回
        List<ClassesSchemeVO> vos = new ArrayList<>();
        for (ClassesScheme classesScheme : schedulingScheme.getClassesscheme_model()) {
            ClassesSchemeVO vo = new ClassesSchemeVO(classesScheme);
            vos.add(vo);
        }
        return vos;
    }

    /**
     * 删除班次方案以及班次
     *
     * @param id 班次方案
     * @return Boolean
     */
    @Override
    public void deleteClassesScheme(Long id) {
        ClassesScheme classesScheme = classesSchemeDao.queryById(id);

        if (Objects.isNull(classesScheme)) {
            return;
        }

        if (CollectionUtils.isNotEmpty(classesScheme.getClassesconfig_model())) {
            List<Long> classesConfigIds = classesScheme.getClassesconfig_model().stream().map(ClassesConfig::getId)
                    .collect(Collectors.toList());

            //删除对应的排版班次表
            List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByClassesConfig(classesConfigIds);
            if (CollectionUtils.isNotEmpty(schedulingClassesList)) {
                throw new BusinessBaseException("当前班次方案下班次已进行排班，不可删除");
            }
            classesConfigDao.deleteBatchIds(classesConfigIds);
        }

        classesSchemeDao.deleteById(id);
    }

    @Override
    public Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto) {
        /*
        1. 判断有无id，有则直接更新，无则查询排班方案后再更新进去
         */
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());

        if (Objects.isNull(dto.getId())) {
            //验证班组名称是否已存在
            if (CollectionUtils.isNotEmpty(schedulingScheme.getTeamgroupinfo_model())) {
                Optional<TeamGroupInfo> first = schedulingScheme.getTeamgroupinfo_model().stream()
                        .filter(s -> Objects.equals(s.getName(), dto.getName())).findFirst();
                if (first.isPresent()) {
                    throw new BusinessBaseException("当前排班方案下，班组名称已存在，请修改！");
                }
            }

            TeamGroupInfo teamGroupInfo = new TeamGroupInfo();
            teamGroupInfo.setColor(dto.getColor());
            teamGroupInfo.setPersonCount(dto.getPersonCount());
            teamGroupInfo.setPersonId(dto.getPersonId());
            teamGroupInfo.setName(dto.getName());
            schedulingScheme.setTeamgroupinfo_model(Collections.singletonList(teamGroupInfo));
            schedulingSchemeDao.updateById(schedulingScheme);

        } else {
            List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByTeamGroup(Collections.singletonList(dto.getId()));
            if (CollectionUtils.isNotEmpty(schedulingClassesList)) {
                throw new BusinessBaseException("当前班组已进行排班，不可修改");
            }

            //更新
            //验证班组名称是否已存在
            if (CollectionUtils.isNotEmpty(schedulingScheme.getTeamgroupinfo_model())) {
                Optional<TeamGroupInfo> first = schedulingScheme.getTeamgroupinfo_model().stream()
                        .filter(s -> !Objects.equals(s.getId(), dto.getId()) && Objects.equals(s.getName(), dto.getName()))
                        .findFirst();
                if (first.isPresent()) {
                    throw new BusinessBaseException("当前排班方案下，班组名称已存在，请修改！");
                }
            }

            TeamGroupInfo teamGroupInfo = new TeamGroupInfo();
            teamGroupInfo.setId(dto.getId());
            teamGroupInfo.setColor(dto.getColor());
            teamGroupInfo.setPersonCount(dto.getPersonCount());
            teamGroupInfo.setPersonId(dto.getPersonId());
            teamGroupInfo.setName(dto.getName());
            teamGroupInfoDao.updateById(teamGroupInfo);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除班组
     *
     * @param id 班组id
     * @return Boolean
     */
    @Override
    public void deleteTeamGroupInfo(Long id) {

        //先删除班组配置的排班表
        List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByTeamGroup(Collections.singletonList(id));
        if (CollectionUtils.isNotEmpty(schedulingClassesList)) {
            throw new BusinessBaseException("当前班组已进行排班，不可删除");
        }
        teamGroupInfoDao.deleteById(id);
    }

    /**
     * 查询班组
     *
     * @param schedulingSchemeId 排班方案id
     * @return 班组
     */
    @Override
    public List<TeamGroupInfoVO> queryTeamGroupInfo(Long schedulingSchemeId) {
        List<TeamGroupInfoVO> teamGroupInfoVOS = new ArrayList<>();
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(schedulingSchemeId);
        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {
            return teamGroupInfoVOS;
        }

        for (TeamGroupInfo teamGroupInfo : schedulingScheme.getTeamgroupinfo_model()) {
            TeamGroupInfoVO teamGroupInfoVO = new TeamGroupInfoVO(teamGroupInfo);
            if (Objects.nonNull(teamGroupInfo.getPersonId())) {
                List<Long> longs;
                try {
                    longs = JsonTransferUtils.transferJsonString(teamGroupInfo.getPersonId(), Long.class);
                } catch (Exception e) {
                    log.info("persionId转换失败");
                    continue;
                }
                Result<List<UserVo>> listResult = cloudAuthService.queryUserBatch(longs);
                ParamUtils.checkResultGenericNoException(listResult);

                List<UserVo> userInfoList = listResult.getData();


                // 1. 预处理：构建userId到UserVo的映射（O(n)时间）
                Map<Long, UserVo> userMap = userInfoList.stream()
                        .collect(Collectors.toMap(UserVo::getId, Function.identity(), (a, b) -> a));

                // 2. 使用StringJoiner处理分隔符（自动省略最后一个顿号）
                StringJoiner sj = new StringJoiner("、");


                longs.forEach(userId ->
                        Optional.ofNullable(userMap.get(userId)) // 安全处理null
                                .map(UserVo::getName)
                                .ifPresent(sj::add)
                );

                // 最终结果（空列表返回空字符串，非空自动拼接）
                teamGroupInfoVO.setPersonName(sj.toString());

            }
            teamGroupInfoVOS.add(teamGroupInfoVO);
        }
        return teamGroupInfoVOS;
    }

    /**
     * 保存排班表
     *
     * @param dto 排班表
     * @return Boolean
     */
    @Override
    public Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto) {
        //先查询本月的所有排班表删除，再保存最新的
        List<SchedulingClasses> deleteSchedulingClassesList = schedulingClassesDao.queryByTimeRange(dto.getStartTime(), dto.getEndTime(), dto.getSchedulingSchemeId());
        if (CollectionUtils.isNotEmpty(deleteSchedulingClassesList)) {
            List<Long> deleteSchedulingClassesIds = deleteSchedulingClassesList.stream().map(SchedulingClasses::getId).collect(Collectors.toList());
            schedulingClassesDao.deleteBatchIds(deleteSchedulingClassesIds);
        }

        List<SchedulingClasses> schedulingClassesList = new ArrayList<>();
        for (SchedulingClassesConfigDTO schedulingClassesConfigDTO : dto.getSchedulingClassesConfigDTO()) {
            SchedulingClasses schedulingClasses = new SchedulingClasses();
            schedulingClasses.setSchedulingSchemeId(dto.getSchedulingSchemeId());
            schedulingClasses.setClassConfigId(schedulingClassesConfigDTO.getClassesConfigId());
            schedulingClasses.setTeamGroupId(schedulingClassesConfigDTO.getTeamGroupId());
            schedulingClasses.setLogTime(schedulingClassesConfigDTO.getLogTime());

            schedulingClassesList.add(schedulingClasses);
        }
        schedulingClassesDao.saveOrUpdateBatch(schedulingClassesList);

        return Boolean.TRUE;
    }

    /**
     * 查询排班表
     *
     * @param starTime 开始时间
     * @param endTime  结束时间
     * @return 排班表
     */
    @Override
    public List<SchedulingClassesVO> querySchedulingClasses(Long starTime, Long endTime, Long schedulingSchemeId) {
        /*
            1. 根据时间范围查询所有的排班表
            2. 按照时间分组，并按照班次顺序排序
            3. 封装班次，班组的数据返回
         */
        List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByTimeRange(starTime, endTime, schedulingSchemeId);
        if (CollectionUtils.isEmpty(schedulingClassesList)) {
            return Collections.emptyList();
        }

        List<Long> teamGroupIds = schedulingClassesList.stream().map(SchedulingClasses::getTeamGroupId).distinct()
                .collect(Collectors.toList());

        List<Long> classConfigIds = schedulingClassesList.stream().map(SchedulingClasses::getClassConfigId).distinct()
                .collect(Collectors.toList());

        Map<Long, List<SchedulingClasses>> schedulingClassesByTimeMap = schedulingClassesList.stream()
                .collect(Collectors.groupingBy(SchedulingClasses::getLogTime));

        List<TeamGroupInfo> teamGroupInfos = teamGroupInfoDao.selectBatchIds(teamGroupIds);
        List<ClassesConfig> classesConfigs = classesConfigDao.selectBatchIds(classConfigIds);

        List<SchedulingClassesVO> voList = new ArrayList<>();
        for (Map.Entry<Long, List<SchedulingClasses>> schedulingClassesByTimeEntry : schedulingClassesByTimeMap.entrySet()) {
            SchedulingClassesVO vo = new SchedulingClassesVO();
            vo.setLogTime(schedulingClassesByTimeEntry.getKey());

            List<SchedulingClassesConfigVO> schedulingClassesConfigVOList = new ArrayList<>();
            for (SchedulingClasses schedulingClasses : schedulingClassesByTimeEntry.getValue()) {
                SchedulingClassesConfigVO classesConfigVO = new SchedulingClassesConfigVO();

                Optional<TeamGroupInfo> teamGroupInfoOp = teamGroupInfos.stream()
                        .filter(t -> Objects.equals(schedulingClasses.getTeamGroupId(), t.getId())).findFirst();
                if (teamGroupInfoOp.isPresent()) {
                    classesConfigVO.setTeamGroupId(teamGroupInfoOp.get().getId());
                    classesConfigVO.setTeamGroupName(teamGroupInfoOp.get().getName());
                    classesConfigVO.setPersonCount(teamGroupInfoOp.get().getPersonCount());
                    classesConfigVO.setPersonId(teamGroupInfoOp.get().getPersonId());
                    classesConfigVO.setColor(teamGroupInfoOp.get().getColor());
                }

                Optional<ClassesConfig> classesConfigOp = classesConfigs.stream()
                        .filter(c -> Objects.equals(c.getId(), schedulingClasses.getClassConfigId())).findFirst();
                if (classesConfigOp.isPresent()) {
                    classesConfigVO.setClassesConfigId(classesConfigOp.get().getId());
                    classesConfigVO.setOrder(classesConfigOp.get().getSerialNumber());
                    classesConfigVO.setClassesConfigName(classesConfigOp.get().getName());
                    classesConfigVO.setStartTime(classesConfigOp.get().getStartTime());
                    classesConfigVO.setEndTime(classesConfigOp.get().getEndTime());
                }
                schedulingClassesConfigVOList.add(classesConfigVO);
            }

            schedulingClassesConfigVOList = schedulingClassesConfigVOList.stream()
                    .sorted(Comparator.comparing(SchedulingClassesConfigVO::getOrder)).collect(Collectors.toList());
            vo.setSchedulingClassesConfigVOS(schedulingClassesConfigVOList);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 查询时间范围内排班班组配置
     *
     * @param starTime 开始时间
     * @param endTime  结束时间
     * @return 班组配置
     */
    @Override
    public List<SchedulingClassesVO> querySchedulingClassesTeamGroupInfo(Long starTime, Long endTime, Long schedulingSchemeId) {
        /*
            1. 根据时间范围查询所有的排班表
            2. 过滤出所有配置了班组的排班
            3. 按照时间分组，并按照班次顺序排序
            4. 封装班次，班组的数据返回
         */
        List<SchedulingClasses> schedulingClassesList = schedulingClassesDao.queryByTimeRange(starTime, endTime, schedulingSchemeId);

        if (CollectionUtils.isEmpty(schedulingClassesList)) {
            return Collections.emptyList();
        }

        schedulingClassesList = schedulingClassesList.stream().filter(s -> Objects.nonNull(s.getTeamGroupId()))
                .collect(Collectors.toList());

        List<Long> teamGroupIds = schedulingClassesList.stream().map(SchedulingClasses::getTeamGroupId).distinct()
                .collect(Collectors.toList());

        List<Long> classConfigIds = schedulingClassesList.stream().map(SchedulingClasses::getClassConfigId).distinct()
                .collect(Collectors.toList());

        Map<Long, List<SchedulingClasses>> schedulingClassesByTimeMap = schedulingClassesList.stream()
                .collect(Collectors.groupingBy(SchedulingClasses::getLogTime));

        List<TeamGroupInfo> teamGroupInfos = teamGroupInfoDao.selectBatchIds(teamGroupIds);
        List<ClassesConfig> classesConfigs = classesConfigDao.selectBatchIds(classConfigIds);

        List<SchedulingClassesVO> voList = new ArrayList<>();
        for (Map.Entry<Long, List<SchedulingClasses>> schedulingClassesByTimeEntry : schedulingClassesByTimeMap.entrySet()) {
            SchedulingClassesVO vo = new SchedulingClassesVO();
            vo.setLogTime(schedulingClassesByTimeEntry.getKey());

            List<SchedulingClassesConfigVO> schedulingClassesConfigVOList = new ArrayList<>();
            for (SchedulingClasses schedulingClasses : schedulingClassesByTimeEntry.getValue()) {
                SchedulingClassesConfigVO classesConfigVO = new SchedulingClassesConfigVO();

                Optional<TeamGroupInfo> teamGroupInfoOp = teamGroupInfos.stream()
                        .filter(t -> Objects.equals(schedulingClasses.getTeamGroupId(), t.getId())).findFirst();
                if (teamGroupInfoOp.isPresent()) {
                    classesConfigVO.setTeamGroupId(teamGroupInfoOp.get().getId());
                    classesConfigVO.setTeamGroupName(teamGroupInfoOp.get().getName());
                    classesConfigVO.setPersonCount(teamGroupInfoOp.get().getPersonCount());
                    classesConfigVO.setPersonId(teamGroupInfoOp.get().getPersonId());
                    classesConfigVO.setColor(teamGroupInfoOp.get().getColor());
                }

                Optional<ClassesConfig> classesConfigOp = classesConfigs.stream()
                        .filter(c -> Objects.equals(c.getId(), schedulingClasses.getClassConfigId())).findFirst();
                if (classesConfigOp.isPresent()) {
                    classesConfigVO.setClassesConfigId(classesConfigOp.get().getId());
                    classesConfigVO.setOrder(classesConfigOp.get().getSerialNumber());
                    classesConfigVO.setClassesConfigName(classesConfigOp.get().getName());
                    classesConfigVO.setStartTime(classesConfigOp.get().getStartTime());
                    classesConfigVO.setEndTime(classesConfigOp.get().getEndTime());
                }

                schedulingClassesConfigVOList.add(classesConfigVO);
            }

            schedulingClassesConfigVOList = schedulingClassesConfigVOList.stream()
                    .sorted(Comparator.comparing(SchedulingClassesConfigVO::getOrder))
                    .collect(Collectors.toList());
            vo.setSchedulingClassesConfigVOS(schedulingClassesConfigVOList);

            voList.add(vo);
        }

        return voList;
    }

    /**
     * 查询生产类型排班方案
     *
     * @return 生产类型排班方案
     */
    @Override
    public List<SchedulingSchemeDetailVO> queryProduceSchedulingSchemeByType(Integer classTeamType) {
        List<SchedulingScheme> schedulingSchemes;
        if (Objects.isNull(classTeamType)) {
            schedulingSchemes = schedulingSchemeDao.queryAll();

        } else {
            schedulingSchemes = schedulingSchemeDao.queryProduceSchedulingScheme(classTeamType);
        }
        if (CollectionUtils.isEmpty(schedulingSchemes)) {
            return Collections.emptyList();
        }
        return getSchedulingSchemeDetailVOS(schedulingSchemes);
    }


    @NotNull
    private List<SchedulingSchemeDetailVO> getSchedulingSchemeDetailVOS(List<SchedulingScheme> schedulingSchemes) {
        List<SchedulingSchemeDetailVO> vos = new ArrayList<>();
        for (SchedulingScheme schedulingScheme : schedulingSchemes) {
            SchedulingSchemeDetailVO vo = new SchedulingSchemeDetailVO();
            vo.setId(schedulingScheme.getId());
            vo.setName(schedulingScheme.getName());
            vo.setOperator(schedulingScheme.getOperator());
            vo.setCreateTime(schedulingScheme.getCreateTime());
            vo.setClassTeamType(schedulingScheme.getClassTeamType());

            if (CollectionUtils.isNotEmpty(schedulingScheme.getClassesscheme_model())) {
                List<ClassesSchemeVO> classesSchemeVOList = new ArrayList<>();
                for (ClassesScheme scheme : schedulingScheme.getClassesscheme_model()) {
                    ClassesSchemeVO classesSchemeVO = new ClassesSchemeVO(scheme);
                    classesSchemeVOList.add(classesSchemeVO);
                }
                vo.setClassesSchemeList(classesSchemeVOList);
            }

            if (CollectionUtils.isNotEmpty(schedulingScheme.getTeamgroupinfo_model())) {
                List<TeamGroupInfoVO> teamGroupInfoVOList = new ArrayList<>();
                for (TeamGroupInfo teamGroupInfo : schedulingScheme.getTeamgroupinfo_model()) {
                    TeamGroupInfoVO teamGroupInfoVO = new TeamGroupInfoVO(teamGroupInfo);
                    teamGroupInfoVOList.add(teamGroupInfoVO);
                }
                vo.setTeamGroupInfoList(teamGroupInfoVOList);
            }
            vos.add(vo);
        }
        return vos;
    }
}