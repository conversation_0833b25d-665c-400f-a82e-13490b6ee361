package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 节假日信息表
 *
 * <AUTHOR>
 * @date 2024/12/20 14:30
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.HOLIDAY_CONFIG)
public class HolidayConfig extends EntityWithName {
    @ApiModelProperty("排班方案表")
    @JsonProperty("schedulingschemeid")
    private Long schedulingSchemeId;

    @ApiModelProperty("节假日具体日期")
    @JsonProperty("holidaydate")
    private Long holidayDate;

    public HolidayConfig() {
        this.modelLabel = ModelLabelDef.HOLIDAY_CONFIG;
    }
}
