package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 排班班次表
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.SCHEDULING_CLASSES)
public class SchedulingClasses extends EntityWithName {

    @ApiModelProperty("排班方案id")
    @JsonProperty("schedulingschemeid")
    private Long schedulingSchemeId;

    @ApiModelProperty("日期")
    @JsonProperty("logtime")
    private Long logTime;

    @ApiModelProperty("班次id")
    @JsonProperty("classconfigid")
    private Long classConfigId;

    @ApiModelProperty("班组id")
    @JsonProperty("teamgroupid")
    private Long teamGroupId;

    public SchedulingClasses() {
        this.modelLabel = ModelLabelDef.SCHEDULING_CLASSES;
    }
}
