"""
遗留代码搜索器

在legacy源码中搜索原始方法实现，提供完整的方法代码和上下文。
基于error_prone_json_reporter的LegacyCodeSearcher核心功能，适配源码上下文提取器的需求。
"""

import os
import logging
import re
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

from .models import ErrorItem, ExtractorConfig


@dataclass
class OriginalMethod:
    """原始方法信息"""
    file_path: str
    method_code: str
    method_signature: str
    javadoc: Optional[str]
    annotations: List[str]
    dependencies: List[str]
    class_name: str
    package_name: str
    line_number: int


@dataclass
class MethodImplementation:
    """方法实现详情"""
    full_method_code: str
    method_body: str
    parameters: List[str]
    return_type: str
    modifiers: List[str]
    throws_exceptions: List[str]
    local_variables: List[str]
    method_calls: List[str]


class LegacyCodeSearcher:
    """
    遗留代码搜索器
    
    职责：在legacy源码中搜索原始方法实现
    输入：ErrorItem和legacy路径
    输出：原始方法代码
    """
    
    def __init__(self, config: ExtractorConfig):
        """
        初始化遗留代码搜索器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Legacy源码路径
        self.legacy_paths = self._get_legacy_paths()
        
        # 搜索缓存
        self.search_cache = {}
        self.file_content_cache = {}
        
        # 索引缓存
        self.method_index = {}
        self.class_index = {}
        
        self.logger.info(f"遗留代码搜索器初始化完成，legacy路径: {self.legacy_paths}")
        
        # 构建索引
        self._build_indexes()
    
    def _get_legacy_paths(self) -> List[str]:
        """获取legacy源码路径"""
        paths = []
        
        # 从配置中获取legacy_src_path
        legacy_src_path = self.config.legacy_src_path
        if legacy_src_path and os.path.exists(legacy_src_path):
            paths.append(legacy_src_path)
            self.logger.info(f"添加legacy路径: {legacy_src_path}")
        else:
            self.logger.warning(f"Legacy路径不存在: {legacy_src_path}")
        
        return paths
    
    def _build_indexes(self):
        """构建方法和类索引以提高搜索效率"""
        try:
            self.logger.info("开始构建legacy代码索引")
            
            for legacy_path in self.legacy_paths:
                self._index_directory(legacy_path)
            
            self.logger.info(f"索引构建完成，方法数: {len(self.method_index)}, 类数: {len(self.class_index)}")
            
        except Exception as e:
            self.logger.error(f"构建索引失败: {str(e)}")
    
    def _index_directory(self, directory: str):
        """索引目录中的Java文件"""
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.java'):
                        file_path = os.path.join(root, file)
                        self._index_java_file(file_path)
                        
        except Exception as e:
            self.logger.warning(f"索引目录失败 {directory}: {str(e)}")
    
    def _index_java_file(self, file_path: str):
        """索引单个Java文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 提取包名
            package_match = re.search(r'package\s+([\w.]+);', content)
            package_name = package_match.group(1) if package_match else ''
            
            # 提取类名
            class_matches = re.finditer(r'(public\s+|private\s+|protected\s+)?(class|interface|enum)\s+(\w+)', content)
            for class_match in class_matches:
                class_name = class_match.group(3)
                full_class_name = f"{package_name}.{class_name}" if package_name else class_name
                
                # 添加到类索引
                if full_class_name not in self.class_index:
                    self.class_index[full_class_name] = []
                self.class_index[full_class_name].append(file_path)
            
            # 提取方法
            method_matches = re.finditer(
                r'(public|private|protected|static|final|abstract|synchronized|\s)+\s+(\w+(?:<[^>]+>)?)\s+(\w+)\s*\([^)]*\)\s*(?:throws\s+[^{]+)?\s*\{',
                content,
                re.MULTILINE
            )
            
            for method_match in method_matches:
                method_name = method_match.group(3)
                method_key = f"{package_name}.{method_name}" if package_name else method_name
                
                # 添加到方法索引
                if method_key not in self.method_index:
                    self.method_index[method_key] = []
                
                self.method_index[method_key].append({
                    'file_path': file_path,
                    'method_name': method_name,
                    'package_name': package_name,
                    'start_pos': method_match.start(),
                    'match_text': method_match.group(0)
                })
                
        except Exception as e:
            self.logger.warning(f"索引Java文件失败 {file_path}: {str(e)}")
    
    def search_service_method(self, service_object_name: str, method_name: str, 
                             current_file_path: str) -> Optional[OriginalMethod]:
        """
        搜索服务对象的方法实现
        
        Args:
            service_object_name: 服务对象名称 (如 nodeService)
            method_name: 方法名称 (如 getProjectTree)
            current_file_path: 当前文件路径，用于分析服务类型
            
        Returns:
            服务方法信息，如果找不到则返回None
        """
        try:
            self.logger.info(f"搜索服务方法: {service_object_name}.{method_name}")
            
            # 首先分析当前文件中的服务对象类型
            service_class_name = self._analyze_service_type(service_object_name, current_file_path)
            if not service_class_name:
                self.logger.warning(f"无法确定服务对象 {service_object_name} 的类型")
                return None
            
            self.logger.info(f"确定服务类型: {service_class_name}")
            
            # 在legacy代码中搜索服务实现类
            service_impl_files = self._find_service_implementation_files(service_class_name)
            
            for service_file in service_impl_files:
                self.logger.debug(f"在服务文件中搜索方法: {service_file}")
                method_info = self._extract_method_from_service_file(service_file, method_name)
                if method_info:
                    return method_info
            
            return None
            
        except Exception as e:
            self.logger.error(f"搜索服务方法失败: {str(e)}")
            return None
    
    def _analyze_service_type(self, service_object_name: str, file_path: str) -> Optional[str]:
        """分析服务对象的类型"""
        try:
            content = self._get_file_content(file_path)
            if not content:
                return None
            
            # 查找字段声明或注入注解
            patterns = [
                rf'@Autowired\s+private\s+(\w+)\s+{re.escape(service_object_name)}\s*;',
                rf'@Resource\s+private\s+(\w+)\s+{re.escape(service_object_name)}\s*;',
                rf'private\s+(\w+)\s+{re.escape(service_object_name)}\s*;',
                rf'(\w+)\s+{re.escape(service_object_name)}\s*='
            ]
            
            for pattern in patterns:
                match = re.search(pattern, content, re.MULTILINE)
                if match:
                    service_type = match.group(1)
                    self.logger.debug(f"找到服务类型: {service_type}")
                    return service_type
            
            return None
            
        except Exception as e:
            self.logger.warning(f"分析服务类型失败: {str(e)}")
            return None
    
    def _find_service_implementation_files(self, service_class_name: str) -> List[str]:
        """查找服务实现类文件"""
        try:
            impl_files = []
            
            # 常见的实现类命名模式
            impl_patterns = [
                f"{service_class_name}Impl",
                f"{service_class_name}Implementation", 
                service_class_name  # 有时接口和实现类同名
            ]
            
            for legacy_path in self.legacy_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_name = file[:-5]  # 移除.java后缀
                            if file_name in impl_patterns:
                                full_path = os.path.join(root, file)
                                impl_files.append(full_path)
                                self.logger.debug(f"找到候选服务文件: {full_path}")
            
            return impl_files
            
        except Exception as e:
            self.logger.warning(f"查找服务实现文件失败: {str(e)}")
            return []
    
    def _extract_method_from_service_file(self, service_file: str, method_name: str) -> Optional[OriginalMethod]:
        """从服务文件中提取方法"""
        try:
            content = self._get_file_content(service_file)
            if not content:
                return None
            
            # 查找方法定义
            method_pattern = rf'(public|private|protected)\s+[^{{]*\s+{re.escape(method_name)}\s*\([^)]*\)\s*[^{{]*\{{'
            
            for match in re.finditer(method_pattern, content, re.MULTILINE):
                method_start = match.start()
                line_number = content[:method_start].count('\n') + 1
                
                # 提取完整方法代码
                method_end = self._find_method_end(content.split('\n'), line_number - 1)
                lines = content.split('\n')
                method_code = '\n'.join(lines[line_number - 1:method_end + 1])
                
                # 提取包名和类名
                package_match = re.search(r'package\s+([\w.]+);', content)
                package_name = package_match.group(1) if package_match else ''
                
                class_match = re.search(r'class\s+(\w+)', content)
                class_name = class_match.group(1) if class_match else ''
                
                return OriginalMethod(
                    file_path=service_file,
                    method_code=method_code,
                    method_signature=match.group(0),
                    javadoc=None,
                    annotations=[],
                    dependencies=[],
                    class_name=class_name,
                    package_name=package_name,
                    line_number=line_number
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"从服务文件提取方法失败: {str(e)}")
            return None

    def search_original_method(self, error_item: ErrorItem, 
                              legacy_paths: Optional[List[str]] = None) -> Optional[OriginalMethod]:
        """
        搜索原始方法实现
        
        Args:
            error_item: 错误项信息
            legacy_paths: 可选的legacy路径列表
            
        Returns:
            原始方法信息，如果找不到则返回None
        """
        try:
            self.logger.debug(f"搜索原始方法: {error_item.package}.{error_item.class_name}.{error_item.missing_method}")
            
            # 构建缓存键
            cache_key = f"{error_item.package}.{error_item.class_name}.{error_item.missing_method}"
            if cache_key in self.search_cache:
                return self.search_cache[cache_key]
            
            # 使用提供的路径或默认路径
            search_paths = legacy_paths or self.legacy_paths
            
            # 策略1: 使用索引快速搜索
            original_method = self._search_by_index(error_item)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            # 策略2: 基于包名和类名的精确搜索
            original_method = self._search_by_package_class(error_item, search_paths)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            # 策略3: 模糊搜索
            original_method = self._fuzzy_search_method(error_item, search_paths)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            self.logger.debug(f"未找到原始方法: {cache_key}")
            return None
            
        except Exception as e:
            self.logger.error(f"搜索原始方法失败: {str(e)}")
            return None
    
    def search_method_with_overloading(self, error_item: ErrorItem, 
                                     legacy_paths: Optional[List[str]] = None) -> List[OriginalMethod]:
        """
        搜索方法的所有重载版本
        
        Args:
            error_item: 错误项信息
            legacy_paths: 可选的legacy路径列表
            
        Returns:
            所有匹配的方法重载版本列表
        """
        try:
            self.logger.debug(f"搜索方法重载: {error_item.package}.{error_item.class_name}.{error_item.missing_method}")
            
            # 使用提供的路径或默认路径
            search_paths = legacy_paths or self.legacy_paths
            
            all_methods = []
            
            # 在所有可能的文件中搜索方法重载
            for legacy_path in search_paths:
                methods = self._find_all_method_overloads(error_item, legacy_path)
                all_methods.extend(methods)
            
            # 根据参数匹配度排序
            if all_methods:
                all_methods = self._rank_methods_by_parameter_match(error_item, all_methods)
            
            return all_methods
            
        except Exception as e:
            self.logger.error(f"搜索方法重载失败: {str(e)}")
            return []
    
    def _find_all_method_overloads(self, error_item: ErrorItem, legacy_path: str) -> List[OriginalMethod]:
        """在指定路径中查找所有方法重载"""
        try:
            methods = []
            
            # 构建可能的文件路径
            package_path = error_item.package.replace('.', '/')
            class_file = f"{error_item.class_name}.java"
            relative_path = f"{package_path}/{class_file}"
            
            candidate_paths = [
                os.path.join(legacy_path, relative_path),
                os.path.join(legacy_path, 'src/main/java', relative_path),
                os.path.join(legacy_path, 'src', relative_path),
                os.path.join(legacy_path, 'java', relative_path)
            ]
            
            for candidate_path in candidate_paths:
                if os.path.exists(candidate_path):
                    file_methods = self._extract_all_method_overloads_from_file(
                        candidate_path, error_item.missing_method, error_item.class_name
                    )
                    methods.extend(file_methods)
            
            return methods
            
        except Exception as e:
            self.logger.warning(f"查找方法重载失败: {str(e)}")
            return []
    
    def _extract_all_method_overloads_from_file(self, file_path: str, method_name: str, class_name: str) -> List[OriginalMethod]:
        """从文件中提取所有方法重载"""
        try:
            content = self._get_file_content(file_path)
            if not content:
                return []
            
            # 验证类名匹配
            class_found = (
                re.search(rf'class\s+{re.escape(class_name)}\b', content) or
                re.search(rf'interface\s+{re.escape(class_name)}\b', content) or
                class_name in file_path
            )
            
            if not class_found:
                return []
            
            methods = []
            
            # 查找所有同名方法
            method_pattern = rf'((?:/\*\*.*?\*/\s*)?(?:@\w+.*?\n\s*)*)((?:public|private|protected|static|final|abstract|synchronized|\s)+)\s+(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^{{]+))?\s*\{{'
            
            for method_match in re.finditer(method_pattern, content, re.MULTILINE | re.DOTALL):
                try:
                    # 提取各部分
                    javadoc_and_annotations = method_match.group(1) or ''
                    modifiers_str = method_match.group(2).strip()
                    return_type = method_match.group(3)
                    parameters_str = method_match.group(5)
                    throws_str = method_match.group(6) or ''
                    
                    # 提取Javadoc
                    javadoc_match = re.search(r'/\*\*(.*?)\*/', javadoc_and_annotations, re.DOTALL)
                    javadoc = javadoc_match.group(1).strip() if javadoc_match else None
                    
                    # 提取注解
                    annotation_matches = re.findall(r'@(\w+)(?:\([^)]*\))?', javadoc_and_annotations)
                    annotations = [f"@{ann}" for ann in annotation_matches]
                    
                    # 提取完整方法代码
                    method_start = method_match.start()
                    method_body_start = method_match.end()
                    method_end = self._find_method_end(content, method_body_start)
                    full_method_code = content[method_start:method_end]
                    
                    # 提取包名
                    package_match = re.search(r'package\s+([\w.]+);', content)
                    package_name = package_match.group(1) if package_match else ''
                    
                    # 获取行号
                    line_number = content[:method_start].count('\n') + 1
                    
                    original_method = OriginalMethod(
                        file_path=file_path,
                        method_code=full_method_code,
                        method_signature=f"{modifiers_str} {return_type} {method_name}({parameters_str})",
                        javadoc=javadoc,
                        annotations=annotations,
                        dependencies=[],
                        class_name=class_name,
                        package_name=package_name,
                        line_number=line_number
                    )
                    
                    methods.append(original_method)
                    
                except Exception as e:
                    self.logger.warning(f"提取方法重载失败: {str(e)}")
                    continue
            
            return methods
            
        except Exception as e:
            self.logger.error(f"从文件提取方法重载失败: {str(e)}")
            return []
    
    def _rank_methods_by_parameter_match(self, error_item: ErrorItem, methods: List[OriginalMethod]) -> List[OriginalMethod]:
        """根据参数匹配度对方法进行排序"""
        try:
            method_scores = []
            
            for method in methods:
                score = self._calculate_parameter_match_score(error_item, method)
                method_scores.append((method, score))
            
            # 按分数降序排序
            method_scores.sort(key=lambda x: x[1], reverse=True)
            
            return [method for method, score in method_scores]
            
        except Exception as e:
            self.logger.warning(f"方法排序失败: {str(e)}")
            return methods
    
    def _calculate_parameter_match_score(self, error_item: ErrorItem, method: OriginalMethod) -> float:
        """计算参数匹配分数"""
        try:
            # 从方法签名中提取参数
            method_params = self._extract_parameters_from_signature(method.method_signature)
            error_params = error_item.in_param
            
            if not error_params and not method_params:
                return 1.0  # 都没有参数，完全匹配
            
            if not error_params or not method_params:
                return 0.1  # 一个有参数一个没有，低分
            
            # 参数数量匹配
            if len(error_params) != len(method_params):
                return 0.3  # 参数数量不匹配，低分
            
            # 参数类型匹配
            type_match_score = 0.0
            for i, (error_param_name, error_param_type) in enumerate(error_params.items()):
                if i < len(method_params):
                    method_param_type = method_params[i].split()[-2] if len(method_params[i].split()) >= 2 else method_params[i]
                    type_score = self._calculate_type_match_score(error_param_type, method_param_type)
                    type_match_score += type_score
            
            # 平均类型匹配分数
            avg_type_score = type_match_score / len(error_params) if error_params else 0.0
            
            # 返回类型匹配
            return_type_score = self._calculate_return_type_match_score(error_item.out_return, method.method_signature)
            
            # 综合分数
            final_score = (avg_type_score * 0.7) + (return_type_score * 0.3)
            
            return final_score
            
        except Exception as e:
            self.logger.warning(f"计算参数匹配分数失败: {str(e)}")
            return 0.0
    
    def _extract_parameters_from_signature(self, method_signature: str) -> List[str]:
        """从方法签名中提取参数列表"""
        try:
            # 查找参数部分
            param_match = re.search(r'\(([^)]*)\)', method_signature)
            if not param_match:
                return []
            
            params_str = param_match.group(1).strip()
            if not params_str:
                return []
            
            # 分割参数
            params = []
            for param in params_str.split(','):
                param = param.strip()
                if param:
                    params.append(param)
            
            return params
            
        except Exception as e:
            self.logger.warning(f"提取参数失败: {str(e)}")
            return []
    
    def _calculate_type_match_score(self, error_type: str, method_type: str) -> float:
        """计算类型匹配分数"""
        try:
            # 完全匹配
            if error_type == method_type:
                return 1.0
            
            # 简化类型名匹配（去掉包名）
            error_simple = error_type.split('.')[-1]
            method_simple = method_type.split('.')[-1]
            
            if error_simple == method_simple:
                return 0.9
            
            # 泛型匹配
            if '<' in error_type and '<' in method_type:
                error_base = error_type.split('<')[0]
                method_base = method_type.split('<')[0]
                if error_base == method_base:
                    return 0.8
            
            # 继承关系匹配（简单的常见类型）
            inheritance_score = self._check_inheritance_match(error_type, method_type)
            if inheritance_score > 0:
                return inheritance_score
            
            # 部分匹配
            if error_simple.lower() in method_simple.lower() or method_simple.lower() in error_simple.lower():
                return 0.3
            
            return 0.0
            
        except Exception as e:
            self.logger.warning(f"计算类型匹配分数失败: {str(e)}")
            return 0.0
    
    def _check_inheritance_match(self, error_type: str, method_type: str) -> float:
        """检查继承关系匹配"""
        try:
            # 常见的继承关系映射
            inheritance_map = {
                'java.lang.String': ['String', 'CharSequence'],
                'java.util.List': ['List', 'Collection', 'Iterable'],
                'java.util.Map': ['Map'],
                'java.util.Set': ['Set', 'Collection', 'Iterable'],
                'java.lang.Integer': ['Integer', 'Number', 'int'],
                'java.lang.Long': ['Long', 'Number', 'long'],
                'java.lang.Double': ['Double', 'Number', 'double'],
                'java.lang.Boolean': ['Boolean', 'boolean'],
            }
            
            # 检查是否有继承关系
            for base_type, derived_types in inheritance_map.items():
                if error_type in [base_type] + derived_types and method_type in [base_type] + derived_types:
                    return 0.7
            
            return 0.0
            
        except Exception as e:
            self.logger.warning(f"检查继承关系失败: {str(e)}")
            return 0.0
    
    def _calculate_return_type_match_score(self, error_return_type: str, method_signature: str) -> float:
        """计算返回类型匹配分数"""
        try:
            # 从方法签名中提取返回类型
            signature_parts = method_signature.split()
            if len(signature_parts) < 2:
                return 0.0
            
            # 查找返回类型（在方法名之前的最后一个类型）
            method_name_index = -1
            for i, part in enumerate(signature_parts):
                if '(' in part:  # 找到方法名（包含参数括号）
                    method_name_index = i
                    break
            
            if method_name_index <= 0:
                return 0.0
            
            method_return_type = signature_parts[method_name_index - 1]
            
            return self._calculate_type_match_score(error_return_type, method_return_type)
            
        except Exception as e:
            self.logger.warning(f"计算返回类型匹配分数失败: {str(e)}")
            return 0.0
    
    def _search_by_index(self, error_item: ErrorItem) -> Optional[OriginalMethod]:
        """使用索引搜索方法"""
        try:
            # 构建搜索键
            full_class_name = f"{error_item.package}.{error_item.class_name}"
            method_key = f"{error_item.package}.{error_item.missing_method}"
            
            # 首先查找类
            if full_class_name in self.class_index:
                for file_path in self.class_index[full_class_name]:
                    original_method = self._extract_method_from_file(
                        file_path, error_item.missing_method, error_item.class_name
                    )
                    if original_method:
                        return original_method
            
            # 然后查找方法
            if method_key in self.method_index:
                for method_entry in self.method_index[method_key]:
                    original_method = self._extract_method_from_file(
                        method_entry['file_path'], error_item.missing_method, error_item.class_name
                    )
                    if original_method:
                        return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"索引搜索失败: {str(e)}")
            return None
    
    def _search_by_package_class(self, error_item: ErrorItem, search_paths: List[str]) -> Optional[OriginalMethod]:
        """基于包名和类名的精确搜索"""
        try:
            # 构建可能的文件路径
            package_path = error_item.package.replace('.', '/')
            class_file = f"{error_item.class_name}.java"
            relative_path = f"{package_path}/{class_file}"
            
            # 在各个legacy路径中搜索
            for legacy_path in search_paths:
                candidate_paths = [
                    os.path.join(legacy_path, relative_path),
                    os.path.join(legacy_path, 'src/main/java', relative_path),
                    os.path.join(legacy_path, 'src', relative_path),
                    os.path.join(legacy_path, 'java', relative_path)
                ]
                
                for candidate_path in candidate_paths:
                    if os.path.exists(candidate_path):
                        original_method = self._extract_method_from_file(
                            candidate_path, error_item.missing_method, error_item.class_name
                        )
                        if original_method:
                            return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"精确搜索失败: {str(e)}")
            return None
    
    def _fuzzy_search_method(self, error_item: ErrorItem, search_paths: List[str]) -> Optional[OriginalMethod]:
        """基于整行内容的精确搜索"""
        try:
            self.logger.debug(f"开始基于整行内容的精确搜索: {error_item.missing_method}")
            
            # 第一步：从当前项目中获取指定行号的内容
            target_line_content = self._get_target_line_content(error_item)
            if not target_line_content:
                self.logger.warning(f"无法获取目标行内容: {error_item.location.file}:{error_item.location.line}")
                return None
            
            self.logger.debug(f"目标行内容: {target_line_content.strip()}")
            
            # 第二步：在legacy代码中精确搜索这行内容
            return self._search_by_line_content(error_item, target_line_content, search_paths)
            
        except Exception as e:
            self.logger.warning(f"基于整行内容的精确搜索失败: {str(e)}")
            return None
    
    def _get_target_line_content(self, error_item: ErrorItem) -> Optional[str]:
        """从当前项目中获取目标行的内容"""
        try:
            # 构建当前项目中的文件路径
            from .file_locator import FileLocator
            from .config import ConfigManager
            
            # 使用file_locator查找当前项目中的文件
            config_manager = ConfigManager()
            current_config = config_manager.load_config()
            file_locator = FileLocator(current_config)
            
            # 查找文件
            current_file_path = file_locator.find_source_file(
                error_item.location.file, 
                [current_config.src_path]
            )
            
            if not current_file_path:
                self.logger.warning(f"未找到当前项目文件: {error_item.location.file}")
                return None
            
            # 读取文件内容
            with open(current_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 获取指定行号的内容（行号从1开始）
            target_line_number = error_item.location.line
            if 1 <= target_line_number <= len(lines):
                target_line = lines[target_line_number - 1]
                return target_line.rstrip('\n\r')  # 移除换行符但保留其他空白字符
            else:
                self.logger.warning(f"行号超出范围: {target_line_number}, 文件总行数: {len(lines)}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取目标行内容失败: {str(e)}")
            return None
    
    def _search_by_line_content(self, error_item: ErrorItem, target_line_content: str, search_paths: List[str]) -> Optional[OriginalMethod]:
        """在legacy代码中搜索指定的行内容"""
        try:
            # 清理目标行内容，用于匹配
            cleaned_target = target_line_content.strip()
            if not cleaned_target:
                self.logger.warning("目标行内容为空")
                return None
            
            # 在各个legacy路径中搜索
            for legacy_path in search_paths:
                self.logger.debug(f"在legacy路径中搜索: {legacy_path}")
                
                # 遍历所有Java文件
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_path = os.path.join(root, file)
                            
                            # 检查文件名是否匹配类名（优先搜索匹配的类文件）
                            if file == f"{error_item.class_name}.java":
                                result = self._search_line_in_file(file_path, cleaned_target, error_item)
                                if result:
                                    return result
                
                # 如果在匹配的类文件中没找到，再搜索所有Java文件
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java') and file != f"{error_item.class_name}.java":
                            file_path = os.path.join(root, file)
                            result = self._search_line_in_file(file_path, cleaned_target, error_item)
                            if result:
                                return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"在legacy代码中搜索行内容失败: {str(e)}")
            return None
    
    def _search_line_in_file(self, file_path: str, target_line_content: str, error_item: ErrorItem) -> Optional[OriginalMethod]:
        """在单个文件中搜索指定行内容"""
        try:
            content = self._get_file_content(file_path)
            if not content:
                return None
            
            lines = content.split('\n')
            
            # 搜索匹配的行
            for line_number, line in enumerate(lines, 1):
                cleaned_line = line.strip()
                if cleaned_line == target_line_content:
                    self.logger.debug(f"找到匹配行: {file_path}:{line_number}")
                    
                    # 提取包含该行的方法
                    return self._extract_method_containing_line(
                        file_path, content, line_number, error_item
                    )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"在文件中搜索行内容失败 {file_path}: {str(e)}")
            return None
    
    def _extract_method_containing_line(self, file_path: str, content: str, target_line_number: int, error_item: ErrorItem) -> Optional[OriginalMethod]:
        """提取包含指定行号的方法"""
        try:
            lines = content.split('\n')
            
            # 向上搜索方法开始
            method_start_line = None
            method_signature = None
            
            for line_num in range(target_line_number - 1, -1, -1):
                line = lines[line_num].strip()
                
                # 查找方法定义模式
                method_pattern = r'(public|private|protected|static|final|abstract|synchronized|\s)+\s+\w+(?:<[^>]+>)?\s+\w+\s*\([^)]*\)\s*(?:throws\s+[^{]+)?\s*\{'
                if re.search(method_pattern, line):
                    method_start_line = line_num + 1
                    method_signature = line
                    break
            
            if not method_start_line:
                self.logger.warning(f"未找到包含行 {target_line_number} 的方法")
                return None
            
            # 向下搜索方法结束
            method_end_line = self._find_method_end_line(lines, method_start_line - 1)
            
            # 提取方法代码
            method_lines = lines[method_start_line - 1:method_end_line]
            method_code = '\n'.join(method_lines)
            
            # 提取包名
            package_match = re.search(r'package\s+([\w.]+);', content)
            package_name = package_match.group(1) if package_match else ''
            
            # 提取类名
            class_match = re.search(r'class\s+(\w+)', content)
            class_name = class_match.group(1) if class_match else error_item.class_name
            
            # 提取Javadoc（向上搜索）
            javadoc = self._extract_javadoc_before_line(lines, method_start_line - 1)
            
            return OriginalMethod(
                file_path=file_path,
                method_code=method_code,
                method_signature=method_signature or '',
                javadoc=javadoc,
                annotations=[],
                dependencies=[],
                class_name=class_name,
                package_name=package_name,
                line_number=target_line_number  # 使用实际找到匹配行的行号
            )
            
        except Exception as e:
            self.logger.error(f"提取包含指定行的方法失败: {str(e)}")
            return None
    
    def _find_method_end_line(self, lines: List[str], method_start_index: int) -> int:
        """查找方法结束行号"""
        try:
            brace_count = 0
            found_opening_brace = False
            
            for i in range(method_start_index, len(lines)):
                line = lines[i]
                
                for char in line:
                    if char == '{':
                        brace_count += 1
                        found_opening_brace = True
                    elif char == '}':
                        brace_count -= 1
                        
                        if found_opening_brace and brace_count == 0:
                            return i + 1  # 返回行号（从1开始）
            
            # 如果没找到结束，返回文件末尾
            return len(lines)
            
        except Exception as e:
            self.logger.warning(f"查找方法结束行失败: {str(e)}")
            return method_start_index + 50  # 返回一个合理的默认值
    
    def _extract_javadoc_before_line(self, lines: List[str], method_start_index: int) -> Optional[str]:
        """提取方法前的Javadoc"""
        try:
            javadoc_lines = []
            in_javadoc = False
            
            # 向上搜索Javadoc
            for i in range(method_start_index - 1, -1, -1):
                line = lines[i].strip()
                
                if line.endswith('*/'):
                    in_javadoc = True
                    javadoc_lines.insert(0, line)
                elif in_javadoc:
                    javadoc_lines.insert(0, line)
                    if line.startswith('/**'):
                        break
                elif line == '' or line.startswith('@'):
                    # 跳过空行和注解
                    continue
                else:
                    # 遇到其他内容，停止搜索
                    break
            
            if javadoc_lines:
                return '\n'.join(javadoc_lines)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"提取Javadoc失败: {str(e)}")
            return None
    
    def _extract_method_from_file(self, file_path: str, method_name: str, class_name: str) -> Optional[OriginalMethod]:
        """从文件中提取方法"""
        try:
            content = self._get_file_content(file_path)
            if not content:
                return None
            
            # 验证类名匹配 - 放宽匹配条件
            class_found = (
                re.search(rf'class\s+{re.escape(class_name)}\b', content) or
                re.search(rf'interface\s+{re.escape(class_name)}\b', content) or
                class_name in file_path  # 如果文件名包含类名也算匹配
            )
            
            if not class_found:
                self.logger.debug(f"类名不匹配: {class_name} in {file_path}")
                return None
            
            # 查找方法 - 使用更灵活的模式
            method_patterns = [
                # 标准方法定义
                rf'((?:/\*\*.*?\*/\s*)?(?:@\w+.*?\n\s*)*)((?:public|private|protected|static|final|abstract|synchronized|\s)+)\s+(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^{{]+))?\s*\{{',
                # 接口方法定义
                rf'((?:/\*\*.*?\*/\s*)?(?:@\w+.*?\n\s*)*)\s*(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^;]+))?\s*;',
                # 简化的方法定义
                rf'(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*\{{'
            ]
            
            method_match = None
            for method_pattern in method_patterns:
                method_match = re.search(method_pattern, content, re.MULTILINE | re.DOTALL)
                if method_match:
                    break
            
            if not method_match:
                self.logger.debug(f"未找到方法定义: {method_name} in {file_path}")
                return None
            
            # 提取各部分
            if len(method_match.groups()) >= 6:  # 标准方法定义
                javadoc_and_annotations = method_match.group(1) or ''
                modifiers_str = method_match.group(2).strip()
                return_type = method_match.group(3)
                parameters_str = method_match.group(5)
                throws_str = method_match.group(6) or ''
            else:  # 简化的方法定义
                javadoc_and_annotations = ''
                modifiers_str = 'public'
                return_type = method_match.group(1)
                parameters_str = method_match.group(3)
                throws_str = ''
            
            # 提取Javadoc
            javadoc_match = re.search(r'/\*\*(.*?)\*/', javadoc_and_annotations, re.DOTALL)
            javadoc = javadoc_match.group(1).strip() if javadoc_match else None
            
            # 提取注解
            annotation_matches = re.findall(r'@(\w+)(?:\([^)]*\))?', javadoc_and_annotations)
            annotations = [f"@{ann}" for ann in annotation_matches]
            
            # 提取完整方法代码
            method_start = method_match.start()
            method_body_start = method_match.end()
            
            # 查找方法结束位置
            method_end = self._find_method_end(content, method_body_start)
            full_method_code = content[method_start:method_end]
            
            # 提取包名
            package_match = re.search(r'package\s+([\w.]+);', content)
            package_name = package_match.group(1) if package_match else ''
            
            # 获取行号
            line_number = content[:method_start].count('\n') + 1
            
            return OriginalMethod(
                file_path=file_path,
                method_code=full_method_code,
                method_signature=f"{modifiers_str} {return_type} {method_name}({parameters_str})",
                javadoc=javadoc,
                annotations=annotations,
                dependencies=[],  # 可以后续扩展
                class_name=class_name,
                package_name=package_name,
                line_number=line_number
            )
            
        except Exception as e:
            self.logger.error(f"从文件提取方法失败: {str(e)}")
            return None
    
    def _get_file_content(self, file_path: str) -> Optional[str]:
        """获取文件内容，带缓存"""
        try:
            # 检查缓存
            if file_path in self.file_content_cache:
                return self.file_content_cache[file_path]
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 缓存内容（限制缓存大小）
            if len(self.file_content_cache) < 100:
                self.file_content_cache[file_path] = content
            
            return content
            
        except Exception as e:
            self.logger.warning(f"读取文件失败 {file_path}: {str(e)}")
            return None
    
    def _find_method_end(self, content: str, start_pos: int) -> int:
        """查找方法结束位置"""
        try:
            brace_count = 1  # 已经有一个开括号
            pos = start_pos
            
            while pos < len(content) and brace_count > 0:
                char = content[pos]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                pos += 1
            
            return pos
            
        except Exception as e:
            self.logger.warning(f"查找方法结束位置失败: {str(e)}")
            return start_pos + 100  # 返回一个合理的默认值
    
    def clear_cache(self):
        """清理缓存"""
        self.search_cache.clear()
        self.file_content_cache.clear()
        self.logger.debug("遗留代码搜索器缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'search_cache_size': len(self.search_cache),
            'file_content_cache_size': len(self.file_content_cache),
            'method_index_size': len(self.method_index),
            'class_index_size': len(self.class_index),
            'legacy_paths_count': len(self.legacy_paths)
        }
    
    def rebuild_indexes(self):
        """重建索引"""
        self.method_index.clear()
        self.class_index.clear()
        self._build_indexes()
        self.logger.info("索引重建完成")
    
    def find_best_matching_method(self, error_item: ErrorItem, 
                                legacy_paths: Optional[List[str]] = None) -> Optional[OriginalMethod]:
        """
        查找最佳匹配的方法（考虑重载和参数匹配）
        
        Args:
            error_item: 错误项信息
            legacy_paths: 可选的legacy路径列表
            
        Returns:
            最佳匹配的方法，如果找不到则返回None
        """
        try:
            # 首先尝试精确匹配
            exact_match = self.search_original_method(error_item, legacy_paths)
            if exact_match:
                return exact_match
            
            # 如果没有精确匹配，查找所有重载并选择最佳匹配
            all_overloads = self.search_method_with_overloading(error_item, legacy_paths)
            if all_overloads:
                # 返回排序后的第一个（最佳匹配）
                return all_overloads[0]
            
            return None
            
        except Exception as e:
            self.logger.error(f"查找最佳匹配方法失败: {str(e)}")
            return None
    
    def extract_method_implementation_details(self, file_path: str, method_name: str) -> Optional[MethodImplementation]:
        """
        提取方法实现详情
        
        Args:
            file_path: 文件路径
            method_name: 方法名
            
        Returns:
            方法实现详情
        """
        try:
            self.logger.debug(f"提取方法实现详情: {file_path}:{method_name}")
            
            # 读取文件内容
            content = self._get_file_content(file_path)
            if not content:
                return None
            
            # 查找方法定义
            method_pattern = rf'((?:public|private|protected|static|final|abstract|synchronized|\s)+)\s+(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^{{]+))?\s*\{{'
            
            method_match = re.search(method_pattern, content, re.MULTILINE | re.DOTALL)
            if not method_match:
                return None
            
            # 提取方法各部分
            modifiers_str = method_match.group(1).strip()
            return_type = method_match.group(2)
            parameters_str = method_match.group(4)
            throws_str = method_match.group(5) or ''
            
            # 解析修饰符
            modifiers = [mod.strip() for mod in modifiers_str.split() if mod.strip()]
            
            # 解析参数
            parameters = self._parse_parameters(parameters_str)
            
            # 解析异常
            throws_exceptions = [ex.strip() for ex in throws_str.split(',') if ex.strip()]
            
            # 提取完整方法代码
            method_start = method_match.start()
            method_body_start = method_match.end()
            
            # 查找方法结束位置
            method_end = self._find_method_end(content, method_body_start)
            full_method_code = content[method_start:method_end]
            
            # 提取方法体
            method_body = content[method_body_start:method_end - 1]  # 去掉最后的}
            
            # 提取局部变量和方法调用
            local_variables = self._extract_local_variables_from_code(method_body)
            method_calls = self._extract_method_calls_from_code(method_body)
            
            return MethodImplementation(
                full_method_code=full_method_code,
                method_body=method_body,
                parameters=parameters,
                return_type=return_type,
                modifiers=modifiers,
                throws_exceptions=throws_exceptions,
                local_variables=local_variables,
                method_calls=method_calls
            )
            
        except Exception as e:
            self.logger.error(f"提取方法实现详情失败: {str(e)}")
            return None
    
    def _parse_parameters(self, parameters_str: str) -> List[str]:
        """解析方法参数"""
        try:
            if not parameters_str.strip():
                return []
            
            parameters = []
            for param in parameters_str.split(','):
                param = param.strip()
                if param:
                    # 移除final等修饰符
                    param = re.sub(r'\bfinal\s+', '', param)
                    parameters.append(param)
            
            return parameters
            
        except Exception as e:
            self.logger.warning(f"解析参数失败: {str(e)}")
            return []
    
    def _extract_local_variables_from_code(self, code: str) -> List[str]:
        """从代码中提取局部变量"""
        try:
            variables = []
            
            # 变量声明模式
            var_patterns = [
                r'\b(int|long|double|float|boolean|String|List|Map|Set|Object)\s+(\w+)',
                r'\b(\w+(?:<[^>]+>)?)\s+(\w+)\s*=',
                r'\bfinal\s+(\w+(?:<[^>]+>)?)\s+(\w+)'
            ]
            
            for pattern in var_patterns:
                matches = re.findall(pattern, code)
                for match in matches:
                    if isinstance(match, tuple) and len(match) >= 2:
                        var_type = match[0] if match[0] else match[1]
                        var_name = match[1] if match[0] else match[0]
                        variables.append(f"{var_type} {var_name}")
            
            return list(set(variables))
            
        except Exception as e:
            self.logger.warning(f"提取局部变量失败: {str(e)}")
            return []
    
    def _extract_method_calls_from_code(self, code: str) -> List[str]:
        """从代码中提取方法调用"""
        try:
            method_calls = []
            
            # 方法调用模式
            call_patterns = [
                r'(\w+)\.(\w+)\s*\(',
                r'(\w+)\s*\(',
                r'this\.(\w+)\s*\(',
                r'super\.(\w+)\s*\('
            ]
            
            for pattern in call_patterns:
                matches = re.findall(pattern, code)
                for match in matches:
                    if isinstance(match, tuple):
                        if len(match) == 2 and match[0] and match[1]:
                            method_calls.append(f"{match[0]}.{match[1]}()")
                        elif len(match) == 1 and match[0]:
                            method_calls.append(f"{match[0]}()")
                    elif match:
                        method_calls.append(f"{match}()")
            
            return list(set(method_calls))
            
        except Exception as e:
            self.logger.warning(f"提取方法调用失败: {str(e)}")
            return []
    
    def search_methods_by_pattern(self, pattern: str, legacy_paths: Optional[List[str]] = None) -> List[OriginalMethod]:
        """
        根据模式搜索方法
        
        Args:
            pattern: 搜索模式（支持正则表达式）
            legacy_paths: 可选的legacy路径列表
            
        Returns:
            匹配的方法列表
        """
        try:
            self.logger.debug(f"根据模式搜索方法: {pattern}")
            
            search_paths = legacy_paths or self.legacy_paths
            matching_methods = []
            
            for legacy_path in search_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_path = os.path.join(root, file)
                            methods = self._find_methods_by_pattern_in_file(file_path, pattern)
                            matching_methods.extend(methods)
            
            return matching_methods
            
        except Exception as e:
            self.logger.error(f"根据模式搜索方法失败: {str(e)}")
            return []
    
    def _find_methods_by_pattern_in_file(self, file_path: str, pattern: str) -> List[OriginalMethod]:
        """在文件中根据模式查找方法"""
        try:
            content = self._get_file_content(file_path)
            if not content:
                return []
            
            methods = []
            
            # 使用正则表达式搜索方法
            method_pattern = rf'((?:/\*\*.*?\*/\s*)?(?:@\w+.*?\n\s*)*)((?:public|private|protected|static|final|abstract|synchronized|\s)+)\s+(\w+(?:<[^>]+>)?)\s+({pattern})\s*\(([^)]*)\)\s*(?:throws\s+([^{{]+))?\s*\{{'
            
            for method_match in re.finditer(method_pattern, content, re.MULTILINE | re.DOTALL):
                try:
                    # 提取方法信息
                    javadoc_and_annotations = method_match.group(1) or ''
                    modifiers_str = method_match.group(2).strip()
                    return_type = method_match.group(3)
                    method_name = method_match.group(4)
                    parameters_str = method_match.group(5)
                    
                    # 提取完整方法代码
                    method_start = method_match.start()
                    method_body_start = method_match.end()
                    method_end = self._find_method_end(content, method_body_start)
                    full_method_code = content[method_start:method_end]
                    
                    # 提取包名和类名
                    package_match = re.search(r'package\s+([\w.]+);', content)
                    package_name = package_match.group(1) if package_match else ''
                    
                    class_match = re.search(r'class\s+(\w+)', content)
                    class_name = class_match.group(1) if class_match else 'Unknown'
                    
                    # 获取行号
                    line_number = content[:method_start].count('\n') + 1
                    
                    original_method = OriginalMethod(
                        file_path=file_path,
                        method_code=full_method_code,
                        method_signature=f"{modifiers_str} {return_type} {method_name}({parameters_str})",
                        javadoc=None,  # 可以后续提取
                        annotations=[],  # 可以后续提取
                        dependencies=[],
                        class_name=class_name,
                        package_name=package_name,
                        line_number=line_number
                    )
                    
                    methods.append(original_method)
                    
                except Exception as e:
                    self.logger.warning(f"提取模式匹配方法失败: {str(e)}")
                    continue
            
            return methods
            
        except Exception as e:
            self.logger.warning(f"在文件中根据模式查找方法失败: {str(e)}")
            return []