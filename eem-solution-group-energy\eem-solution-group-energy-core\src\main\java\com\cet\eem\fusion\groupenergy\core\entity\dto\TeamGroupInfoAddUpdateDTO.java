package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增修改班组DTO
 *
 * <AUTHOR>
 */
@ApiModel(value = "TeamGroupInfoAddUpdateDTO", description = "新增修改班组DTO")
@Data
public class TeamGroupInfoAddUpdateDTO {

    @ApiModelProperty(value = "排班方案id", required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "班组id，更新传递", required = false)
    private Long id;

    @ApiModelProperty(value = "班组名称", required = true)
    private String name;

    @ApiModelProperty(value = "班组人数", required = false)
    private Integer personCount;

    @ApiModelProperty(value = "运维人员清单", required = false)
    private String personId;

    @ApiModelProperty(value = "班组颜色标识", required = true)
    private String color;

}
