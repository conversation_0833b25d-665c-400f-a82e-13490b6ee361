package com.cet.eem.fusion.groupenergy.core.service;

import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;
import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;
import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;
import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;
import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 班组能耗查询业务层
 *
 * <AUTHOR>
 */
public interface TeamEnergyService {

    /**
     * 查询班组能耗数据
     *
     * @param dto 查询条件
     * @return 班组能耗数据
     */
    TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto);

    /**
     * 班组用能柱状图查询
     *
     * @param dto 查询条件
     * @return 班组用能柱状图
     */
    List<TeamGroupEnergyHistogramVO> queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto);

    /**
     * 查询班次对比能耗数据
     *
     * @param dto 查询条件
     * @return 班次能耗对比数据
     */
    List<ClassesEnergyInfoVO> queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto);

    /**
     * 根据条件查询班次能耗
     *
     * @param dto 查询条件
     * @return 班次能耗
     */
    ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto);


    /**
     * 根据能源类型和排班方案查询节点树
     *
     * @param energyType         能源类型
     * @param schedulingSchemeId 排班方案id
     * @return 节点树
     */
    List<Map<String, Object>> classesEnergyProjectTree(Integer energyType, Long schedulingSchemeId);
}
