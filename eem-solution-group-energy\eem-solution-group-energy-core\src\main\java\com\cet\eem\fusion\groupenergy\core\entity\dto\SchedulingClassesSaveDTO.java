package com.cet.eem.fusion.groupenergy.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 排班表保存dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "SchedulingClassesSaveDTO", description = "排班表保存dto")
@Data
public class SchedulingClassesSaveDTO {

    @ApiModelProperty(value = "开始时间" ,required = true)
    private Long startTime;

    @ApiModelProperty(value = "结束时间" ,required = true)
    private Long endTime;

    @ApiModelProperty(value = "排班方案id" ,required = true)
    private Long schedulingSchemeId;

    @ApiModelProperty(value = "排班配置" ,required = true)
    private List<SchedulingClassesConfigDTO> schedulingClassesConfigDTO;
}
