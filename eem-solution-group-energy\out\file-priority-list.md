# 文件优先级排序和组织

## 任务执行概述

**任务**: 2.1 按文件优先级排序和组织  
**执行时间**: 2025-08-27  
**数据来源**: 所有第一轮处理的任务文件  
**排序策略**: 常量类 → 实体类 → 工具类 → DAO → Service → Controller

## 数据来源汇总

### 任务文件统计
- **task-import.md**: 111个类问题，涉及22个文件
- **task-message.md**: 0个问题（无消息推送相关问题）
- **task-permission.md**: 9个权限ID问题，涉及2个文件
- **task-unit.md**: 3个单位服务变更问题，涉及1个文件
- **task-quantity.md**: 0个问题（无物理量查询服务相关问题）
- **task-other.md**: 22个其他问题，涉及4个文件

### 总体统计
- **总问题数**: 145个
- **涉及文件数**: 25个（去重后）
- **需要修复的文件**: 25个

## 按优先级排序的文件列表

### 1. 常量类 (Constants) - 优先级最高

#### 1.1 GroupEnergyConstantDef.java
- **文件路径**: eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/def/GroupEnergyConstantDef.java
- **问题类型**: 权限ID调整
- **问题数量**: 4个常量需要修改
- **修复内容**: 
  - SCHEDULING_SCHEME: 122 → 10122
  - CLASSES_SCHEME: 123 → 10123
  - TEAM_GROUP_INFO: 124 → 10124
  - SCHEDULING_CLASSES: 125 → 10125
- **依赖关系**: 被TeamConfigController引用
- **修复优先级**: 🔴 最高（其他文件依赖此常量）

### 2. 实体类 (Entity/PO/VO/DTO) - 优先级高

#### 2.1 SchedulingScheme.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: SchedulingSchemeAddUpdateDTO类导入
- **依赖关系**: 被多个DAO和Service使用
- **修复优先级**: 🟢 高

#### 2.2 ClassesConfigVO.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: ClassesConfig类导入
- **依赖关系**: VO层，被Controller使用
- **修复优先级**: 🟢 高

#### 2.3 ClassesSchemeVO.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: ClassesScheme类导入
- **依赖关系**: VO层，被Controller使用
- **修复优先级**: 🟢 高

#### 2.4 SchedulingSchemeVO.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: SchedulingScheme类导入
- **依赖关系**: VO层，被Controller使用
- **修复优先级**: 🟢 高

### 3. 工具类 (Utils) - 优先级中等

*当前项目中无工具类需要修复*

### 4. DAO层 - 优先级中等

#### 4.1 ClassesConfigDao.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: ClassesConfig类导入
- **依赖关系**: 被ClassesConfigDaoImpl实现
- **修复优先级**: 🟢 中等

#### 4.2 ClassesConfigDaoImpl.java
- **问题类型**: Import问题
- **问题数量**: 2个
- **修复内容**: ClassesConfigDao、ClassesConfig类导入
- **依赖关系**: 实现ClassesConfigDao，被Service使用
- **修复优先级**: 🟢 中等

#### 4.3 ClassesSchemeDao.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: ClassesScheme类导入
- **依赖关系**: 被ClassesSchemeDaoImpl实现
- **修复优先级**: 🟢 中等

#### 4.4 ClassesSchemeDaoImpl.java
- **问题类型**: Import问题
- **问题数量**: 2个
- **修复内容**: ClassesSchemeDao、ClassesScheme类导入
- **依赖关系**: 实现ClassesSchemeDao，被Service使用
- **修复优先级**: 🟢 中等

#### 4.5 HolidayConfigDao.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: HolidayConfig类导入
- **依赖关系**: 被HolidayConfigDaoImpl实现
- **修复优先级**: 🟢 中等

#### 4.6 HolidayConfigDaoImpl.java
- **问题类型**: Import问题
- **问题数量**: 2个
- **修复内容**: HolidayConfigDao、HolidayConfig类导入
- **依赖关系**: 实现HolidayConfigDao，被Service使用
- **修复优先级**: 🟢 中等

#### 4.7 SchedulingClassesDao.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: SchedulingClasses类导入
- **依赖关系**: 被SchedulingClassesDaoImpl实现
- **修复优先级**: 🟢 中等

#### 4.8 SchedulingClassesDaoImpl.java
- **问题类型**: Import问题
- **问题数量**: 2个
- **修复内容**: SchedulingClassesDao、SchedulingClasses类导入
- **依赖关系**: 实现SchedulingClassesDao，被Service使用
- **修复优先级**: 🟢 中等

#### 4.9 SchedulingSchemeDao.java
- **问题类型**: Import问题
- **问题数量**: 3个
- **修复内容**: SchedulingScheme、SchedulingSchemeQueryDTO、ResultWithTotal类导入
- **依赖关系**: 被SchedulingSchemeDaoImpl实现
- **修复优先级**: 🟢 中等

#### 4.10 SchedulingSchemeDaoImpl.java
- **问题类型**: Import问题
- **问题数量**: 4个
- **修复内容**: SchedulingSchemeDao、SchedulingScheme、SchedulingSchemeQueryDTO、ResultWithTotal类导入
- **依赖关系**: 实现SchedulingSchemeDao，被Service使用
- **修复优先级**: 🟢 中等

#### 4.11 SchedulingSchemeToNodeDao.java
- **问题类型**: Import问题
- **问题数量**: 1个
- **修复内容**: SchedulingSchemeToNode类导入
- **依赖关系**: 被SchedulingSchemeToNodeDaoImpl实现
- **修复优先级**: 🟢 中等

#### 4.12 SchedulingSchemeToNodeDaoImpl.java
- **问题类型**: Import问题
- **问题数量**: 2个
- **修复内容**: SchedulingSchemeToNodeDao、SchedulingSchemeToNode类导入
- **依赖关系**: 实现SchedulingSchemeToNodeDao，被Service使用
- **修复优先级**: 🟢 中等

### 5. Service层 - 优先级低

#### 5.1 TeamConfigService.java
- **问题类型**: Import问题
- **问题数量**: 12个
- **修复内容**: 多个DTO、VO类导入，ResultWithTotal替换
- **依赖关系**: 被TeamConfigServiceImpl实现，被Controller使用
- **修复优先级**: 🟡 低（部分需要AI判断）

#### 5.2 TeamConfigServiceImpl.java
- **问题类型**: Import问题 + 其他问题
- **问题数量**: 33个（25个Import + 8个其他）
- **修复内容**: 
  - Import问题: 多个实体类、DTO、VO导入
  - 其他问题: @Resource注解替换、废弃服务替换
- **依赖关系**: 实现TeamConfigService，被Controller使用
- **修复优先级**: 🔴 低（复杂度高，需要仔细处理）

#### 5.3 TeamEnergyServiceImpl.java
- **问题类型**: 单位服务变更 + 其他问题
- **问题数量**: 8个（3个单位服务 + 5个其他）
- **修复内容**:
  - 单位服务变更: UnitService → EnergyUnitService
  - 其他问题: @Resource注解替换、废弃服务替换
- **依赖关系**: 被Controller使用
- **修复优先级**: 🔴 低（涉及业务逻辑变更）

### 6. Controller层 - 优先级最低

#### 6.1 TeamConfigController.java
- **问题类型**: Import问题 + 权限ID问题 + 其他问题
- **问题数量**: 22个（12个Import + 9个权限ID + 1个其他）
- **修复内容**:
  - Import问题: 多个DTO、VO类导入
  - 权限ID问题: 9个方法的权限ID调整
  - 其他问题: @Resource注解替换
- **依赖关系**: 依赖Service层和常量类
- **修复优先级**: 🟡 最低（依赖其他层修复完成）

#### 6.2 TeamEnergyController.java
- **问题类型**: Import问题 + 其他问题
- **问题数量**: 7个（6个Import + 1个其他）
- **修复内容**:
  - Import问题: 多个DTO、VO类导入，UnitService替换
  - 其他问题: @Resource注解替换
- **依赖关系**: 依赖Service层
- **修复优先级**: 🟢 最低

## 依赖关系分析

### 关键依赖链
1. **GroupEnergyConstantDef** → **TeamConfigController** (权限ID常量)
2. **实体类/VO** → **DAO** → **Service** → **Controller** (标准分层架构)
3. **废弃服务替换** 影响 **Service层** 和 **Controller层**

### 修复顺序建议
1. **第一批**: 常量类 (GroupEnergyConstantDef)
2. **第二批**: 实体类和VO类 (4个文件)
3. **第三批**: DAO层 (12个文件)
4. **第四批**: Service层 (3个文件)
5. **第五批**: Controller层 (2个文件)

## 风险评估

### 🔴 高风险文件
- **TeamConfigServiceImpl**: 33个问题，涉及废弃服务替换
- **TeamEnergyServiceImpl**: 8个问题，涉及单位服务变更
- **GroupEnergyConstantDef**: 影响权限ID，需要谨慎修改

### 🟡 中等风险文件
- **TeamConfigController**: 22个问题，但大部分是标准修复
- **TeamConfigService**: 12个问题，部分需要AI判断

### 🟢 低风险文件
- **所有DAO层文件**: 主要是Import问题，风险较低
- **所有VO类**: 简单的Import问题
- **TeamEnergyController**: 问题较少，风险低

## 执行建议

1. **严格按优先级顺序执行**，确保依赖关系正确
2. **常量类优先**，避免其他文件编译错误
3. **分批处理**，每批完成后进行编译验证
4. **重点关注高风险文件**，需要额外的业务逻辑验证
5. **保持文件间的一致性**，确保修复方案协调统一

---

**任务2.1执行完成** ✅  
**输出**: 按优先级排序的25个文件列表  
**下一步**: 执行任务2.2生成详细的task-step.md修复任务文件
