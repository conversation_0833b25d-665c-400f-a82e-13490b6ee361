"""
简化的配置管理工具
"""
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from models import ProcessingConfig

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False


class SimpleConfigManager:
    """简化的配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config_data = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        # 默认配置
        default_config = {
            "processing": {
                "timeout_seconds": 30,
                "max_results": 10,
                "knowledge_base_path": "./能管代码迁移知识库.md"
            },
            "paths": {
                "script_path": "./FindNameFromJar2.py",
                "project_path": "../"
            },
            "logging": {
                "level": "INFO",
                "file": "error_analysis.log",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        }
        
        self.config_data = default_config
        
        # 如果指定了配置文件，尝试加载
        if self.config_file and Path(self.config_file).exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if (self.config_file.endswith('.yaml') or self.config_file.endswith('.yml')) and HAS_YAML:
                        file_config = yaml.safe_load(f)
                    else:
                        file_config = json.load(f)
                
                # 合并配置
                self._merge_config(self.config_data, file_config)
            except Exception as e:
                print(f"警告: 无法加载配置文件 {self.config_file}: {e}")
        
        # 环境变量覆盖
        self._apply_env_overrides()
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """合并配置字典"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _apply_env_overrides(self) -> None:
        """应用环境变量覆盖"""
        env_mappings = {
            "JAVA_ANALYZER_TIMEOUT": ("processing", "timeout_seconds"),
            "JAVA_ANALYZER_MAX_RESULTS": ("processing", "max_results"),
            "JAVA_ANALYZER_SCRIPT_PATH": ("paths", "script_path"),
            "JAVA_ANALYZER_PROJECT_PATH": ("paths", "project_path"),
            "JAVA_ANALYZER_LOG_LEVEL": ("logging", "level"),
            "JAVA_ANALYZER_LOG_FILE": ("logging", "file")
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                if section not in self.config_data:
                    self.config_data[section] = {}
                
                # 类型转换
                if key in ["timeout_seconds", "max_results"]:
                    try:
                        value = int(value)
                    except ValueError:
                        continue
                
                self.config_data[section][key] = value
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键
            default: 默认值
        
        Returns:
            配置值
        """
        return self.config_data.get(section, {}).get(key, default)
    
    def get_processing_config(self) -> ProcessingConfig:
        """
        获取处理配置对象
        
        Returns:
            ProcessingConfig实例
        """
        processing = self.config_data.get("processing", {})
        paths = self.config_data.get("paths", {})
        
        return ProcessingConfig(
            script_path=paths.get("script_path", "./FindNameFromJar2.py"),
            project_path=paths.get("project_path", "../"),
            timeout_seconds=processing.get("timeout_seconds", 30),
            max_results=processing.get("max_results", 10),
            knowledge_base_path=processing.get("knowledge_base_path", "./能管代码迁移知识库.md"),
            log_level=self.config_data.get("logging", {}).get("level", "INFO"),
            log_file=self.config_data.get("logging", {}).get("file", "error_analysis.log")
        )