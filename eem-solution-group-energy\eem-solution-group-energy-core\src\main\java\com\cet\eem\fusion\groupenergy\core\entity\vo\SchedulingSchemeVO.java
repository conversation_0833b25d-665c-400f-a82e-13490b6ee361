package com.cet.eem.fusion.groupenergy.core.entity.vo;

import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 排班方案vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "SchedulingSchemeVO", description = "排班方案vo")
public class SchedulingSchemeVO {

    @ApiModelProperty(value = "方案id")
    private Long id;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "班组类型（1-运维，2-生产）")
    private Integer classTeamType;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "创建人id")
    private Long operator;

    public SchedulingSchemeVO(SchedulingScheme schedulingScheme) {
        this.id = schedulingScheme.getId();
        this.name = schedulingScheme.getName();
        this.classTeamType = schedulingScheme.getClassTeamType();
        this.createTime = schedulingScheme.getCreateTime();
        this.operator = schedulingScheme.getOperator();
    }
}
